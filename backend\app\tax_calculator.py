"""
Pakistan Income Tax Calculator for FY 2025-26
Implements tax calculation logic based on official tax slabs
"""

class TaxCalculator:
    """
    Pakistan Income Tax Calculator for FY 2025-26

    Tax Slabs:
    - 0 – 600,000: 0%
    - 600,001 – 1,200,000: 1% of (income - 600,000)
    - 1,200,001 – 2,200,000: 6,000 + 11% of (income - 1,200,000)
    - 2,200,001 – 3,200,000: 116,000 + 23% of (income - 2,200,000)
    - 3,200,001 – 4,100,000: 346,000 + 30% of (income - 3,200,000)
    - Above 4,100,000: 616,000 + 35% of (income - 4,100,000)

    Surcharge: 9% of base tax (only applicable when annual income exceeds Rs. 10 million)
    """
    
    # Tax slabs for FY 2025-26
    TAX_SLABS = [
        {"min": 0, "max": 600000, "rate": 0.0, "base_tax": 0},
        {"min": 600001, "max": 1200000, "rate": 0.01, "base_tax": 0},
        {"min": 1200001, "max": 2200000, "rate": 0.11, "base_tax": 6000},
        {"min": 2200001, "max": 3200000, "rate": 0.23, "base_tax": 116000},
        {"min": 3200001, "max": 4100000, "rate": 0.30, "base_tax": 346000},
        {"min": 4100001, "max": float('inf'), "rate": 0.35, "base_tax": 616000}
    ]
    
    SURCHARGE_RATE = 0.09  # 9% surcharge
    SURCHARGE_THRESHOLD = 10000000  # 10 million PKR threshold for surcharge
    
    @classmethod
    def validate_input(cls, monthly_gross):
        """
        Validate monthly gross salary input
        
        Args:
            monthly_gross (float): Monthly gross salary
            
        Returns:
            tuple: (is_valid, error_message)
        """
        if not isinstance(monthly_gross, (int, float)):
            return False, "Monthly gross salary must be a number"
        
        if monthly_gross < 0:
            return False, "Monthly gross salary cannot be negative"
        
        if monthly_gross > 10000000:  # 10 million PKR monthly limit
            return False, "Monthly gross salary exceeds maximum limit (10,000,000 PKR)"
        
        return True, None
    
    @classmethod
    def calculate_annual_tax(cls, annual_income):
        """
        Calculate annual tax based on income and tax slabs
        
        Args:
            annual_income (float): Annual income in PKR
            
        Returns:
            float: Annual tax amount
        """
        if annual_income <= 0:
            return 0
        
        # Find applicable tax slab
        for slab in cls.TAX_SLABS:
            # For the first slab, check if income is <= max
            if slab["min"] == 0 and annual_income <= slab["max"]:
                return 0  # No tax for first slab
            # For other slabs, check if income falls within range
            elif slab["min"] > 0 and annual_income > (slab["min"] - 1) and annual_income <= slab["max"]:
                # Calculate tax on amount above previous slab's limit
                prev_slab_max = slab["min"] - 1
                taxable_amount = annual_income - prev_slab_max
                tax = slab["base_tax"] + (taxable_amount * slab["rate"])
                return tax
        
        # Should not reach here, but handle edge case
        return 0
    
    @classmethod
    def calculate_tax(cls, monthly_gross):
        """
        Calculate complete tax breakdown for monthly gross salary
        
        Args:
            monthly_gross (float): Monthly gross salary in PKR
            
        Returns:
            dict: Tax calculation breakdown
        """
        # Validate input
        is_valid, error_message = cls.validate_input(monthly_gross)
        if not is_valid:
            raise ValueError(error_message)
        
        # Calculate annual income
        annual_income = monthly_gross * 12
        
        # Calculate base tax
        base_tax = cls.calculate_annual_tax(annual_income)

        # Calculate surcharge (9% of base tax, only if annual income exceeds 10 million)
        if annual_income > cls.SURCHARGE_THRESHOLD:
            surcharge = base_tax * cls.SURCHARGE_RATE
        else:
            surcharge = 0
        
        # Calculate total annual tax
        total_tax = base_tax + surcharge
        
        # Calculate monthly tax
        monthly_tax = total_tax / 12
        
        # Calculate net monthly salary
        net_monthly_salary = monthly_gross - monthly_tax
        
        return {
            "annual_income": round(annual_income, 2),
            "base_tax": round(base_tax, 2),
            "surcharge": round(surcharge, 2),
            "total_tax": round(total_tax, 2),
            "monthly_tax": round(monthly_tax, 2),
            "net_monthly_salary": round(net_monthly_salary, 2)
        }
    
    @classmethod
    def get_tax_slab_info(cls, annual_income):
        """
        Get information about which tax slab applies to the income
        
        Args:
            annual_income (float): Annual income in PKR
            
        Returns:
            dict: Tax slab information
        """
        for i, slab in enumerate(cls.TAX_SLABS):
            if slab["min"] <= annual_income <= slab["max"]:
                return {
                    "slab_number": i + 1,
                    "min_income": slab["min"],
                    "max_income": slab["max"] if slab["max"] != float('inf') else "Above 4,100,000",
                    "tax_rate": f"{slab['rate'] * 100}%",
                    "base_tax": slab["base_tax"]
                }
        
        return None

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Hamza\\\\my-tax-calculator\\\\frontend\\\\src\\\\components\\\\Header.js\";\n/**\n * Header Component\n * Application header with title and navigation\n */\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    style: {\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(10px)',\n      borderBottom: '1px solid rgba(255, 255, 255, 0.2)',\n      padding: '20px 0',\n      marginBottom: '30px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          flexWrap: 'wrap',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              color: 'white',\n              fontSize: '28px',\n              fontWeight: '700',\n              marginBottom: '4px',\n              textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n            },\n            children: \"Pakistan Income Tax Calculator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: 'rgba(255, 255, 255, 0.9)',\n              fontSize: '16px',\n              margin: 0,\n              fontWeight: '400'\n            },\n            children: \"Calculate your income tax for FY 2025-26\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            fontSize: '14px',\n            color: 'rgba(255, 255, 255, 0.8)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '8px 12px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '6px',\n              border: '1px solid rgba(255, 255, 255, 0.2)'\n            },\n            children: \"FY 2025-26\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '8px 12px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '6px',\n              border: '1px solid rgba(255, 255, 255, 0.2)'\n            },\n            children: \"Pakistan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Header", "style", "background", "<PERSON><PERSON>ilter", "borderBottom", "padding", "marginBottom", "children", "className", "display", "alignItems", "justifyContent", "flexWrap", "gap", "color", "fontSize", "fontWeight", "textShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/Header.js"], "sourcesContent": ["/**\n * Header Component\n * Application header with title and navigation\n */\nimport React from 'react';\n\nconst Header = () => {\n  return (\n    <header style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(10px)',\n      borderBottom: '1px solid rgba(255, 255, 255, 0.2)',\n      padding: '20px 0',\n      marginBottom: '30px'\n    }}>\n      <div className=\"container\">\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          flexWrap: 'wrap',\n          gap: '16px'\n        }}>\n          <div>\n            <h1 style={{\n              color: 'white',\n              fontSize: '28px',\n              fontWeight: '700',\n              marginBottom: '4px',\n              textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n            }}>\n              Pakistan Income Tax Calculator\n            </h1>\n            <p style={{\n              color: 'rgba(255, 255, 255, 0.9)',\n              fontSize: '16px',\n              margin: 0,\n              fontWeight: '400'\n            }}>\n              Calculate your income tax for FY 2025-26\n            </p>\n          </div>\n          \n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            fontSize: '14px',\n            color: 'rgba(255, 255, 255, 0.8)'\n          }}>\n            <div style={{\n              padding: '8px 12px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '6px',\n              border: '1px solid rgba(255, 255, 255, 0.2)'\n            }}>\n              FY 2025-26\n            </div>\n            <div style={{\n              padding: '8px 12px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '6px',\n              border: '1px solid rgba(255, 255, 255, 0.2)'\n            }}>\n              Pakistan\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,KAAK,EAAE;MACbC,UAAU,EAAE,0BAA0B;MACtCC,cAAc,EAAE,YAAY;MAC5BC,YAAY,EAAE,oCAAoC;MAClDC,OAAO,EAAE,QAAQ;MACjBC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,eACAR,OAAA;MAAKS,SAAS,EAAC,WAAW;MAAAD,QAAA,eACxBR,OAAA;QAAKE,KAAK,EAAE;UACVQ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BC,QAAQ,EAAE,MAAM;UAChBC,GAAG,EAAE;QACP,CAAE;QAAAN,QAAA,gBACAR,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAIE,KAAK,EAAE;cACTa,KAAK,EAAE,OAAO;cACdC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBV,YAAY,EAAE,KAAK;cACnBW,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAGE,KAAK,EAAE;cACRa,KAAK,EAAE,0BAA0B;cACjCC,QAAQ,EAAE,MAAM;cAChBO,MAAM,EAAE,CAAC;cACTN,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENtB,OAAA;UAAKE,KAAK,EAAE;YACVQ,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBG,GAAG,EAAE,MAAM;YACXE,QAAQ,EAAE,MAAM;YAChBD,KAAK,EAAE;UACT,CAAE;UAAAP,QAAA,gBACAR,OAAA;YAAKE,KAAK,EAAE;cACVI,OAAO,EAAE,UAAU;cACnBH,UAAU,EAAE,0BAA0B;cACtCqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAKE,KAAK,EAAE;cACVI,OAAO,EAAE,UAAU;cACnBH,UAAU,EAAE,0BAA0B;cACtCqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACI,EAAA,GAjEIzB,MAAM;AAmEZ,eAAeA,MAAM;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
/**
 * Tax Calculator Form Component
 * Handles user input for monthly gross salary and form validation
 */
import React, { useState } from 'react';
import { validation } from '../services/taxApi';

const TaxCalculatorForm = ({ onCalculate, loading, error }) => {
  const [monthlyGross, setMonthlyGross] = useState('');
  const [validationErrors, setValidationErrors] = useState([]);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate input
    const validationResult = validation.validateMonthlyGross(monthlyGross);
    
    if (!validationResult.isValid) {
      setValidationErrors(validationResult.errors);
      return;
    }
    
    // Clear validation errors and submit
    setValidationErrors([]);
    onCalculate(Number(monthlyGross));
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setMonthlyGross(value);
    
    // Clear validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  const hasErrors = validationErrors.length > 0 || error;

  return (
    <div className="card">
      <h2 style={{ marginBottom: '24px', color: '#333', fontSize: '24px' }}>
        Calculate Your Income Tax
      </h2>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="monthlyGross" className="form-label">
            Monthly Gross Salary (PKR)
          </label>
          <input
            type="number"
            id="monthlyGross"
            className={`form-input ${hasErrors ? 'error' : ''}`}
            value={monthlyGross}
            onChange={handleInputChange}
            placeholder="Enter your monthly gross salary"
            min="0"
            max="10000000"
            step="0.01"
            disabled={loading}
          />
          <small style={{ color: '#666', fontSize: '14px', marginTop: '4px', display: 'block' }}>
            Enter your monthly gross salary before any deductions
          </small>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="alert alert-error">
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* API Error */}
        {error && (
          <div className="alert alert-error">
            {error}
          </div>
        )}

        <button
          type="submit"
          className="btn btn-primary"
          disabled={loading || !monthlyGross.trim()}
        >
          {loading && <span className="loading"></span>}
          {loading ? 'Calculating...' : 'Calculate Tax'}
        </button>
      </form>

      {/* Tax Information */}
      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3 style={{ marginBottom: '16px', color: '#333', fontSize: '18px' }}>
          Tax Information (FY 2025-26)
        </h3>
        <div style={{ fontSize: '14px', color: '#666', lineHeight: '1.6' }}>
          <p><strong>Tax Slabs:</strong></p>
          <ul style={{ marginLeft: '20px', marginTop: '8px' }}>
            <li>0 – 600,000: 0%</li>
            <li>600,001 – 1,200,000: 1%</li>
            <li>1,200,001 – 2,200,000: 11%</li>
            <li>2,200,001 – 3,200,000: 23%</li>
            <li>3,200,001 – 4,100,000: 30%</li>
            <li>Above 4,100,000: 35%</li>
          </ul>
          <p style={{ marginTop: '12px' }}>
            <strong>Surcharge:</strong> 9% of base tax (only when annual income exceeds Rs. 10 million)
          </p>
        </div>
      </div>
    </div>
  );
};

export default TaxCalculatorForm;

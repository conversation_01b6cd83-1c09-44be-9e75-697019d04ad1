# Pakistan Income Tax Calculator API Documentation

## Base URL
```
http://localhost:5000/api
```

## Endpoints

### 1. Health Check

**GET** `/health`

Check if the API is running and healthy.

**Response:**
```json
{
  "status": "healthy",
  "message": "Pakistan Income Tax Calculator API is running"
}
```

**Status Codes:**
- `200 OK` - API is healthy

---

### 2. Calculate Tax

**POST** `/tax/calculate`

Calculate income tax based on monthly gross salary.

**Request Body:**
```json
{
  "monthly_gross": 100000
}
```

**Parameters:**
- `monthly_gross` (number, required): Monthly gross salary in PKR
  - Minimum: 0
  - Maximum: 10,000,000

**Response:**
```json
{
  "success": true,
  "data": {
    "annual_income": 1200000,
    "base_tax": 6000.0,
    "surcharge": 0.0,
    "total_tax": 6000.0,
    "monthly_tax": 500.0,
    "net_monthly_salary": 99500.0
  }
}
```

**Response Fields:**
- `annual_income`: Total yearly income (monthly_gross × 12)
- `base_tax`: Tax amount before surcharge
- `surcharge`: 9% surcharge on base tax (only when annual income > Rs. 10 million)
- `total_tax`: Total annual tax (base_tax + surcharge)
- `monthly_tax`: Monthly tax deduction (total_tax ÷ 12)
- `net_monthly_salary`: Take-home salary (monthly_gross - monthly_tax)

**Error Response:**
```json
{
  "success": false,
  "error": "Error message describing the issue"
}
```

**Status Codes:**
- `200 OK` - Calculation successful
- `400 Bad Request` - Invalid input data
- `500 Internal Server Error` - Server error

**Example Requests:**

```bash
# Calculate tax for 100,000 PKR monthly salary
curl -X POST http://localhost:5000/api/tax/calculate \
  -H "Content-Type: application/json" \
  -d '{"monthly_gross": 100000}'

# Calculate tax for 50,000 PKR monthly salary
curl -X POST http://localhost:5000/api/tax/calculate \
  -H "Content-Type: application/json" \
  -d '{"monthly_gross": 50000}'
```

---

### 3. Calculation History

**GET** `/tax/history`

Retrieve recent tax calculation history.

**Query Parameters:**
- `limit` (integer, optional): Number of recent calculations to return
  - Default: 10
  - Minimum: 1
  - Maximum: 100

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "timestamp": "2025-08-02T12:00:00",
      "monthly_gross": 100000,
      "annual_income": 1200000,
      "base_tax": 6000.0,
      "surcharge": 540.0,
      "total_tax": 6540.0,
      "monthly_tax": 545.0,
      "net_monthly_salary": 99455.0
    }
  ]
}
```

**Example Requests:**

```bash
# Get last 10 calculations
curl http://localhost:5000/api/tax/history

# Get last 5 calculations
curl http://localhost:5000/api/tax/history?limit=5
```

---

## Tax Rules (FY 2025-26)

The API implements the following tax structure:

### Tax Slabs
| Income Range (PKR) | Tax Rate | Calculation |
|-------------------|----------|-------------|
| 0 – 600,000 | 0% | No tax |
| 600,001 – 1,200,000 | 1% | 1% of (income - 600,000) |
| 1,200,001 – 2,200,000 | 11% | 6,000 + 11% of (income - 1,200,000) |
| 2,200,001 – 3,200,000 | 23% | 116,000 + 23% of (income - 2,200,000) |
| 3,200,001 – 4,100,000 | 30% | 346,000 + 30% of (income - 3,200,000) |
| Above 4,100,000 | 35% | 616,000 + 35% of (income - 4,100,000) |

### Additional Charges
- **Surcharge**: 9% of the base tax amount (only applicable when annual income exceeds Rs. 10 million)

### Calculation Formula
1. Calculate annual income: `monthly_gross × 12`
2. Determine applicable tax slab and calculate base tax
3. Calculate surcharge: `base_tax × 0.09`
4. Calculate total tax: `base_tax + surcharge`
5. Calculate monthly tax: `total_tax ÷ 12`
6. Calculate net salary: `monthly_gross - monthly_tax`

---

## Error Handling

The API returns appropriate HTTP status codes and error messages:

### Common Errors

**400 Bad Request:**
```json
{
  "success": false,
  "error": "Monthly gross salary must be a number"
}
```

**400 Bad Request:**
```json
{
  "success": false,
  "error": "Monthly gross salary cannot be negative"
}
```

**400 Bad Request:**
```json
{
  "success": false,
  "error": "Monthly gross salary exceeds maximum limit (10,000,000 PKR)"
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "error": "Internal server error"
}
```

---

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing rate limiting to prevent abuse.

---

## CORS

The API includes CORS headers to allow cross-origin requests from the frontend application.

---

## Database

The API uses SQLite to log all tax calculations for audit purposes. The database file (`tax_calculator.db`) is created automatically in the backend directory.

{"ast": null, "code": "/**\n * Header Component\n * Application header with title and navigation\n */import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Header=()=>{return/*#__PURE__*/_jsx(\"header\",{style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',borderBottom:'1px solid rgba(255, 255, 255, 0.2)',padding:'20px 0',marginBottom:'30px'},children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',flexWrap:'wrap',gap:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{style:{color:'white',fontSize:'28px',fontWeight:'700',marginBottom:'4px',textShadow:'0 2px 4px rgba(0, 0, 0, 0.1)'},children:\"Pakistan Income Tax Calculator\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'rgba(255, 255, 255, 0.9)',fontSize:'16px',margin:0,fontWeight:'400'},children:\"Calculate your income tax for FY 2025-26\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'12px',fontSize:'14px',color:'rgba(255, 255, 255, 0.8)'},children:[/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 12px',background:'rgba(255, 255, 255, 0.1)',borderRadius:'6px',border:'1px solid rgba(255, 255, 255, 0.2)'},children:\"FY 2025-26\"}),/*#__PURE__*/_jsx(\"div\",{style:{padding:'8px 12px',background:'rgba(255, 255, 255, 0.1)',borderRadius:'6px',border:'1px solid rgba(255, 255, 255, 0.2)'},children:\"Pakistan\"})]})]})})});};export default Header;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "style", "background", "<PERSON><PERSON>ilter", "borderBottom", "padding", "marginBottom", "children", "className", "display", "alignItems", "justifyContent", "flexWrap", "gap", "color", "fontSize", "fontWeight", "textShadow", "margin", "borderRadius", "border"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/Header.js"], "sourcesContent": ["/**\n * Header Component\n * Application header with title and navigation\n */\nimport React from 'react';\n\nconst Header = () => {\n  return (\n    <header style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(10px)',\n      borderBottom: '1px solid rgba(255, 255, 255, 0.2)',\n      padding: '20px 0',\n      marginBottom: '30px'\n    }}>\n      <div className=\"container\">\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          flexWrap: 'wrap',\n          gap: '16px'\n        }}>\n          <div>\n            <h1 style={{\n              color: 'white',\n              fontSize: '28px',\n              fontWeight: '700',\n              marginBottom: '4px',\n              textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n            }}>\n              Pakistan Income Tax Calculator\n            </h1>\n            <p style={{\n              color: 'rgba(255, 255, 255, 0.9)',\n              fontSize: '16px',\n              margin: 0,\n              fontWeight: '400'\n            }}>\n              Calculate your income tax for FY 2025-26\n            </p>\n          </div>\n          \n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            fontSize: '14px',\n            color: 'rgba(255, 255, 255, 0.8)'\n          }}>\n            <div style={{\n              padding: '8px 12px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '6px',\n              border: '1px solid rgba(255, 255, 255, 0.2)'\n            }}>\n              FY 2025-26\n            </div>\n            <div style={{\n              padding: '8px 12px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '6px',\n              border: '1px solid rgba(255, 255, 255, 0.2)'\n            }}>\n              Pakistan\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACEH,IAAA,WAAQI,KAAK,CAAE,CACbC,UAAU,CAAE,0BAA0B,CACtCC,cAAc,CAAE,YAAY,CAC5BC,YAAY,CAAE,oCAAoC,CAClDC,OAAO,CAAE,QAAQ,CACjBC,YAAY,CAAE,MAChB,CAAE,CAAAC,QAAA,cACAV,IAAA,QAAKW,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBR,KAAA,QAAKE,KAAK,CAAE,CACVQ,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BC,QAAQ,CAAE,MAAM,CAChBC,GAAG,CAAE,MACP,CAAE,CAAAN,QAAA,eACAR,KAAA,QAAAQ,QAAA,eACEV,IAAA,OAAII,KAAK,CAAE,CACTa,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBV,YAAY,CAAE,KAAK,CACnBW,UAAU,CAAE,8BACd,CAAE,CAAAV,QAAA,CAAC,gCAEH,CAAI,CAAC,cACLV,IAAA,MAAGI,KAAK,CAAE,CACRa,KAAK,CAAE,0BAA0B,CACjCC,QAAQ,CAAE,MAAM,CAChBG,MAAM,CAAE,CAAC,CACTF,UAAU,CAAE,KACd,CAAE,CAAAT,QAAA,CAAC,0CAEH,CAAG,CAAC,EACD,CAAC,cAENR,KAAA,QAAKE,KAAK,CAAE,CACVQ,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBG,GAAG,CAAE,MAAM,CACXE,QAAQ,CAAE,MAAM,CAChBD,KAAK,CAAE,0BACT,CAAE,CAAAP,QAAA,eACAV,IAAA,QAAKI,KAAK,CAAE,CACVI,OAAO,CAAE,UAAU,CACnBH,UAAU,CAAE,0BAA0B,CACtCiB,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,oCACV,CAAE,CAAAb,QAAA,CAAC,YAEH,CAAK,CAAC,cACNV,IAAA,QAAKI,KAAK,CAAE,CACVI,OAAO,CAAE,UAAU,CACnBH,UAAU,CAAE,0BAA0B,CACtCiB,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,oCACV,CAAE,CAAAb,QAAA,CAAC,UAEH,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
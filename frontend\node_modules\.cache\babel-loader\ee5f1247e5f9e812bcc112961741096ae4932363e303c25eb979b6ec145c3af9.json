{"ast": null, "code": "var _jsxFileName = \"C:\\\\Hamza\\\\my-tax-calculator\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n/**\n * Main App Component\n * Pakistan Income Tax Calculator Application\n */\nimport React, { useState, useEffect } from 'react';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport TaxCalculatorForm from './components/TaxCalculatorForm';\nimport TaxResults from './components/TaxResults';\nimport { taxApi } from './services/taxApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [taxResults, setTaxResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [apiStatus, setApiStatus] = useState('checking');\n\n  // Check API health on component mount\n  useEffect(() => {\n    checkApiHealth();\n  }, []);\n  const checkApiHealth = async () => {\n    try {\n      await taxApi.healthCheck();\n      setApiStatus('healthy');\n    } catch (err) {\n      setApiStatus('error');\n      console.error('API health check failed:', err);\n    }\n  };\n  const handleCalculateTax = async monthlyGross => {\n    setLoading(true);\n    setError(null);\n    try {\n      const results = await taxApi.calculateTax(monthlyGross);\n      setTaxResults(results);\n    } catch (err) {\n      setError(err.message || 'Failed to calculate tax. Please try again.');\n      setTaxResults(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRetry = () => {\n    setError(null);\n    setTaxResults(null);\n    checkApiHealth();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container\",\n      children: [apiStatus === 'error' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        style: {\n          marginBottom: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-error\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Connection Error:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this), \" Unable to connect to the backend server. Please ensure the Flask server is running on port 5000.\", /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRetry,\n            style: {\n              marginLeft: '12px',\n              padding: '4px 12px',\n              background: 'transparent',\n              border: '1px solid #c53030',\n              borderRadius: '4px',\n              color: '#c53030',\n              cursor: 'pointer',\n              fontSize: '14px'\n            },\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this), apiStatus === 'checking' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        style: {\n          marginBottom: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"loading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this), \"Connecting to backend server...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',\n          gap: '30px',\n          alignItems: 'start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(TaxCalculatorForm, {\n            onCalculate: handleCalculateTax,\n            loading: loading,\n            error: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [taxResults && /*#__PURE__*/_jsxDEV(TaxResults, {\n            results: taxResults\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 28\n          }, this), !taxResults && !loading && !error && apiStatus === 'healthy' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                marginBottom: '16px',\n                color: '#333',\n                fontSize: '24px'\n              },\n              children: \"Welcome to Pakistan Tax Calculator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666',\n                lineHeight: '1.6',\n                marginBottom: '20px',\n                fontSize: '16px'\n              },\n              children: \"Calculate your monthly income tax according to Pakistan's tax rules for Financial Year 2025-26. Simply enter your monthly gross salary to get a detailed breakdown of your tax liability.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '20px',\n                backgroundColor: '#f8fafc',\n                borderRadius: '8px',\n                border: '1px solid #e2e8f0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  marginBottom: '12px',\n                  color: '#334155',\n                  fontSize: '18px',\n                  fontWeight: '600'\n                },\n                children: \"What's Included:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  margin: 0,\n                  paddingLeft: '20px',\n                  color: '#64748b',\n                  lineHeight: '1.6'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Complete tax calculation based on current slabs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"9% surcharge calculation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Monthly and annual tax breakdown\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Net take-home salary calculation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Effective tax rate analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), !taxResults && apiStatus === 'healthy' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        style: {\n          marginTop: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginBottom: '20px',\n            color: '#333',\n            fontSize: '20px'\n          },\n          children: \"Quick Examples\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '16px'\n          },\n          children: [{\n            salary: 50000,\n            description: 'Entry Level'\n          }, {\n            salary: 100000,\n            description: 'Mid Level'\n          }, {\n            salary: 200000,\n            description: 'Senior Level'\n          }, {\n            salary: 350000,\n            description: 'Executive Level'\n          }].map((example, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleCalculateTax(example.salary),\n            disabled: loading,\n            style: {\n              padding: '16px',\n              background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',\n              border: '1px solid #cbd5e1',\n              borderRadius: '8px',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              textAlign: 'center'\n            },\n            onMouseOver: e => {\n              e.target.style.transform = 'translateY(-2px)';\n              e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';\n            },\n            onMouseOut: e => {\n              e.target.style.transform = 'translateY(0)';\n              e.target.style.boxShadow = 'none';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '600',\n                color: '#334155',\n                marginBottom: '4px'\n              },\n              children: [\"PKR \", example.salary.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#64748b'\n              },\n              children: example.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"RPcrY0Nlvc+dl7jbhvpoHQjXVjE=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "Footer", "TaxCalculatorForm", "TaxResults", "taxApi", "jsxDEV", "_jsxDEV", "App", "_s", "taxResults", "setTaxResults", "loading", "setLoading", "error", "setError", "api<PERSON><PERSON>us", "setApiStatus", "checkApiHealth", "healthCheck", "err", "console", "handleCalculateTax", "monthlyGross", "results", "calculateTax", "message", "handleRetry", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "onClick", "marginLeft", "padding", "background", "border", "borderRadius", "color", "cursor", "fontSize", "display", "alignItems", "gridTemplateColumns", "gap", "onCalculate", "lineHeight", "backgroundColor", "fontWeight", "margin", "paddingLeft", "marginTop", "salary", "description", "map", "example", "index", "disabled", "transition", "textAlign", "onMouseOver", "e", "target", "transform", "boxShadow", "onMouseOut", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/App.js"], "sourcesContent": ["/**\n * Main App Component\n * Pakistan Income Tax Calculator Application\n */\nimport React, { useState, useEffect } from 'react';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport TaxCalculatorForm from './components/TaxCalculatorForm';\nimport TaxResults from './components/TaxResults';\nimport { taxApi } from './services/taxApi';\n\nfunction App() {\n  const [taxResults, setTaxResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [apiStatus, setApiStatus] = useState('checking');\n\n  // Check API health on component mount\n  useEffect(() => {\n    checkApiHealth();\n  }, []);\n\n  const checkApiHealth = async () => {\n    try {\n      await taxApi.healthCheck();\n      setApiStatus('healthy');\n    } catch (err) {\n      setApiStatus('error');\n      console.error('API health check failed:', err);\n    }\n  };\n\n  const handleCalculateTax = async (monthlyGross) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const results = await taxApi.calculateTax(monthlyGross);\n      setTaxResults(results);\n    } catch (err) {\n      setError(err.message || 'Failed to calculate tax. Please try again.');\n      setTaxResults(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRetry = () => {\n    setError(null);\n    setTaxResults(null);\n    checkApiHealth();\n  };\n\n  return (\n    <div className=\"App\">\n      <Header />\n      \n      <main className=\"container\">\n        {/* API Status Warning */}\n        {apiStatus === 'error' && (\n          <div className=\"card\" style={{ marginBottom: '20px' }}>\n            <div className=\"alert alert-error\">\n              <strong>Connection Error:</strong> Unable to connect to the backend server. \n              Please ensure the Flask server is running on port 5000.\n              <button \n                onClick={handleRetry}\n                style={{\n                  marginLeft: '12px',\n                  padding: '4px 12px',\n                  background: 'transparent',\n                  border: '1px solid #c53030',\n                  borderRadius: '4px',\n                  color: '#c53030',\n                  cursor: 'pointer',\n                  fontSize: '14px'\n                }}\n              >\n                Retry\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* API Status Loading */}\n        {apiStatus === 'checking' && (\n          <div className=\"card\" style={{ marginBottom: '20px' }}>\n            <div style={{ \n              display: 'flex', \n              alignItems: 'center', \n              color: '#666',\n              fontSize: '14px'\n            }}>\n              <span className=\"loading\"></span>\n              Connecting to backend server...\n            </div>\n          </div>\n        )}\n\n        {/* Main Content */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',\n          gap: '30px',\n          alignItems: 'start'\n        }}>\n          {/* Tax Calculator Form */}\n          <div>\n            <TaxCalculatorForm\n              onCalculate={handleCalculateTax}\n              loading={loading}\n              error={error}\n            />\n          </div>\n\n          {/* Tax Results */}\n          <div>\n            {taxResults && <TaxResults results={taxResults} />}\n            \n            {/* Welcome Message when no results */}\n            {!taxResults && !loading && !error && apiStatus === 'healthy' && (\n              <div className=\"card\">\n                <h2 style={{ marginBottom: '16px', color: '#333', fontSize: '24px' }}>\n                  Welcome to Pakistan Tax Calculator\n                </h2>\n                <p style={{ \n                  color: '#666', \n                  lineHeight: '1.6', \n                  marginBottom: '20px',\n                  fontSize: '16px'\n                }}>\n                  Calculate your monthly income tax according to Pakistan's tax rules for \n                  Financial Year 2025-26. Simply enter your monthly gross salary to get \n                  a detailed breakdown of your tax liability.\n                </p>\n                \n                <div style={{\n                  padding: '20px',\n                  backgroundColor: '#f8fafc',\n                  borderRadius: '8px',\n                  border: '1px solid #e2e8f0'\n                }}>\n                  <h3 style={{ \n                    marginBottom: '12px', \n                    color: '#334155', \n                    fontSize: '18px',\n                    fontWeight: '600'\n                  }}>\n                    What's Included:\n                  </h3>\n                  <ul style={{ \n                    margin: 0, \n                    paddingLeft: '20px',\n                    color: '#64748b',\n                    lineHeight: '1.6'\n                  }}>\n                    <li>Complete tax calculation based on current slabs</li>\n                    <li>9% surcharge calculation</li>\n                    <li>Monthly and annual tax breakdown</li>\n                    <li>Net take-home salary calculation</li>\n                    <li>Effective tax rate analysis</li>\n                  </ul>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Examples */}\n        {!taxResults && apiStatus === 'healthy' && (\n          <div className=\"card\" style={{ marginTop: '30px' }}>\n            <h3 style={{ marginBottom: '20px', color: '#333', fontSize: '20px' }}>\n              Quick Examples\n            </h3>\n            <div style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '16px'\n            }}>\n              {[\n                { salary: 50000, description: 'Entry Level' },\n                { salary: 100000, description: 'Mid Level' },\n                { salary: 200000, description: 'Senior Level' },\n                { salary: 350000, description: 'Executive Level' }\n              ].map((example, index) => (\n                <button\n                  key={index}\n                  onClick={() => handleCalculateTax(example.salary)}\n                  disabled={loading}\n                  style={{\n                    padding: '16px',\n                    background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',\n                    border: '1px solid #cbd5e1',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    textAlign: 'center'\n                  }}\n                  onMouseOver={(e) => {\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = 'none';\n                  }}\n                >\n                  <div style={{ fontWeight: '600', color: '#334155', marginBottom: '4px' }}>\n                    PKR {example.salary.toLocaleString()}\n                  </div>\n                  <div style={{ fontSize: '14px', color: '#64748b' }}>\n                    {example.description}\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,MAAM,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACdkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMb,MAAM,CAACc,WAAW,CAAC,CAAC;MAC1BF,YAAY,CAAC,SAAS,CAAC;IACzB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZH,YAAY,CAAC,OAAO,CAAC;MACrBI,OAAO,CAACP,KAAK,CAAC,0BAA0B,EAAEM,GAAG,CAAC;IAChD;EACF,CAAC;EAED,MAAME,kBAAkB,GAAG,MAAOC,YAAY,IAAK;IACjDV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMS,OAAO,GAAG,MAAMnB,MAAM,CAACoB,YAAY,CAACF,YAAY,CAAC;MACvDZ,aAAa,CAACa,OAAO,CAAC;IACxB,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZL,QAAQ,CAACK,GAAG,CAACM,OAAO,IAAI,4CAA4C,CAAC;MACrEf,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,WAAW,GAAGA,CAAA,KAAM;IACxBZ,QAAQ,CAAC,IAAI,CAAC;IACdJ,aAAa,CAAC,IAAI,CAAC;IACnBO,cAAc,CAAC,CAAC;EAClB,CAAC;EAED,oBACEX,OAAA;IAAKqB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBtB,OAAA,CAACN,MAAM;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEV1B,OAAA;MAAMqB,SAAS,EAAC,WAAW;MAAAC,QAAA,GAExBb,SAAS,KAAK,OAAO,iBACpBT,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAACM,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACpDtB,OAAA;UAAKqB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCtB,OAAA;YAAAsB,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qGAElC,eAAA1B,OAAA;YACE6B,OAAO,EAAET,WAAY;YACrBO,KAAK,EAAE;cACLG,UAAU,EAAE,MAAM;cAClBC,OAAO,EAAE,UAAU;cACnBC,UAAU,EAAE,aAAa;cACzBC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,KAAK,EAAE,SAAS;cAChBC,MAAM,EAAE,SAAS;cACjBC,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjB,SAAS,KAAK,UAAU,iBACvBT,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAACM,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACpDtB,OAAA;UAAK2B,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBJ,KAAK,EAAE,MAAM;YACbE,QAAQ,EAAE;UACZ,CAAE;UAAAf,QAAA,gBACAtB,OAAA;YAAMqB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,mCAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD1B,OAAA;QAAK2B,KAAK,EAAE;UACVW,OAAO,EAAE,MAAM;UACfE,mBAAmB,EAAE,sCAAsC;UAC3DC,GAAG,EAAE,MAAM;UACXF,UAAU,EAAE;QACd,CAAE;QAAAjB,QAAA,gBAEAtB,OAAA;UAAAsB,QAAA,eACEtB,OAAA,CAACJ,iBAAiB;YAChB8C,WAAW,EAAE3B,kBAAmB;YAChCV,OAAO,EAAEA,OAAQ;YACjBE,KAAK,EAAEA;UAAM;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1B,OAAA;UAAAsB,QAAA,GACGnB,UAAU,iBAAIH,OAAA,CAACH,UAAU;YAACoB,OAAO,EAAEd;UAAW;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAGjD,CAACvB,UAAU,IAAI,CAACE,OAAO,IAAI,CAACE,KAAK,IAAIE,SAAS,KAAK,SAAS,iBAC3DT,OAAA;YAAKqB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBtB,OAAA;cAAI2B,KAAK,EAAE;gBAAEC,YAAY,EAAE,MAAM;gBAAEO,KAAK,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAAf,QAAA,EAAC;YAEtE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAG2B,KAAK,EAAE;gBACRQ,KAAK,EAAE,MAAM;gBACbQ,UAAU,EAAE,KAAK;gBACjBf,YAAY,EAAE,MAAM;gBACpBS,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EAAC;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ1B,OAAA;cAAK2B,KAAK,EAAE;gBACVI,OAAO,EAAE,MAAM;gBACfa,eAAe,EAAE,SAAS;gBAC1BV,YAAY,EAAE,KAAK;gBACnBD,MAAM,EAAE;cACV,CAAE;cAAAX,QAAA,gBACAtB,OAAA;gBAAI2B,KAAK,EAAE;kBACTC,YAAY,EAAE,MAAM;kBACpBO,KAAK,EAAE,SAAS;kBAChBE,QAAQ,EAAE,MAAM;kBAChBQ,UAAU,EAAE;gBACd,CAAE;gBAAAvB,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1B,OAAA;gBAAI2B,KAAK,EAAE;kBACTmB,MAAM,EAAE,CAAC;kBACTC,WAAW,EAAE,MAAM;kBACnBZ,KAAK,EAAE,SAAS;kBAChBQ,UAAU,EAAE;gBACd,CAAE;gBAAArB,QAAA,gBACAtB,OAAA;kBAAAsB,QAAA,EAAI;gBAA+C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxD1B,OAAA;kBAAAsB,QAAA,EAAI;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjC1B,OAAA;kBAAAsB,QAAA,EAAI;gBAAgC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzC1B,OAAA;kBAAAsB,QAAA,EAAI;gBAAgC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzC1B,OAAA;kBAAAsB,QAAA,EAAI;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAACvB,UAAU,IAAIM,SAAS,KAAK,SAAS,iBACrCT,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAACM,KAAK,EAAE;UAAEqB,SAAS,EAAE;QAAO,CAAE;QAAA1B,QAAA,gBACjDtB,OAAA;UAAI2B,KAAK,EAAE;YAAEC,YAAY,EAAE,MAAM;YAAEO,KAAK,EAAE,MAAM;YAAEE,QAAQ,EAAE;UAAO,CAAE;UAAAf,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1B,OAAA;UAAK2B,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfE,mBAAmB,EAAE,sCAAsC;YAC3DC,GAAG,EAAE;UACP,CAAE;UAAAnB,QAAA,EACC,CACC;YAAE2B,MAAM,EAAE,KAAK;YAAEC,WAAW,EAAE;UAAc,CAAC,EAC7C;YAAED,MAAM,EAAE,MAAM;YAAEC,WAAW,EAAE;UAAY,CAAC,EAC5C;YAAED,MAAM,EAAE,MAAM;YAAEC,WAAW,EAAE;UAAe,CAAC,EAC/C;YAAED,MAAM,EAAE,MAAM;YAAEC,WAAW,EAAE;UAAkB,CAAC,CACnD,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnBrD,OAAA;YAEE6B,OAAO,EAAEA,CAAA,KAAMd,kBAAkB,CAACqC,OAAO,CAACH,MAAM,CAAE;YAClDK,QAAQ,EAAEjD,OAAQ;YAClBsB,KAAK,EAAE;cACLI,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,mDAAmD;cAC/DC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBE,MAAM,EAAE,SAAS;cACjBmB,UAAU,EAAE,eAAe;cAC3BC,SAAS,EAAE;YACb,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAK;cAClBA,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACiC,SAAS,GAAG,kBAAkB;cAC7CF,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACkC,SAAS,GAAG,+BAA+B;YAC5D,CAAE;YACFC,UAAU,EAAGJ,CAAC,IAAK;cACjBA,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACiC,SAAS,GAAG,eAAe;cAC1CF,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACkC,SAAS,GAAG,MAAM;YACnC,CAAE;YAAAvC,QAAA,gBAEFtB,OAAA;cAAK2B,KAAK,EAAE;gBAAEkB,UAAU,EAAE,KAAK;gBAAEV,KAAK,EAAE,SAAS;gBAAEP,YAAY,EAAE;cAAM,CAAE;cAAAN,QAAA,GAAC,MACpE,EAAC8B,OAAO,CAACH,MAAM,CAACc,cAAc,CAAC,CAAC;YAAA;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACN1B,OAAA;cAAK2B,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEF,KAAK,EAAE;cAAU,CAAE;cAAAb,QAAA,EAChD8B,OAAO,CAACF;YAAW;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA,GA1BD2B,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEP1B,OAAA,CAACL,MAAM;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACxB,EAAA,CAnNQD,GAAG;AAAA+D,EAAA,GAAH/D,GAAG;AAqNZ,eAAeA,GAAG;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
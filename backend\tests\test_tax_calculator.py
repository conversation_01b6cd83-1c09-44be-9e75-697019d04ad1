"""
Unit tests for Pakistan Income Tax Calculator
Tests cover low, mid, and high income edge cases
"""
import pytest
from app.tax_calculator import TaxCalculator

class TestTaxCalculator:
    """Test cases for TaxCalculator class"""
    
    def test_validate_input_valid_cases(self):
        """Test input validation for valid cases"""
        # Valid integer
        is_valid, error = TaxCalculator.validate_input(50000)
        assert is_valid is True
        assert error is None
        
        # Valid float
        is_valid, error = TaxCalculator.validate_input(50000.50)
        assert is_valid is True
        assert error is None
        
        # Zero income
        is_valid, error = TaxCalculator.validate_input(0)
        assert is_valid is True
        assert error is None
    
    def test_validate_input_invalid_cases(self):
        """Test input validation for invalid cases"""
        # Negative income
        is_valid, error = TaxCalculator.validate_input(-1000)
        assert is_valid is False
        assert "cannot be negative" in error
        
        # String input
        is_valid, error = TaxCalculator.validate_input("50000")
        assert is_valid is False
        assert "must be a number" in error
        
        # Excessive income
        is_valid, error = TaxCalculator.validate_input(15000000)
        assert is_valid is False
        assert "exceeds maximum limit" in error
    
    def test_calculate_annual_tax_slab_1(self):
        """Test tax calculation for first slab (0 - 600,000)"""
        # Zero income
        tax = TaxCalculator.calculate_annual_tax(0)
        assert tax == 0
        
        # Income within first slab
        tax = TaxCalculator.calculate_annual_tax(500000)
        assert tax == 0
        
        # Maximum of first slab
        tax = TaxCalculator.calculate_annual_tax(600000)
        assert tax == 0
    
    def test_calculate_annual_tax_slab_2(self):
        """Test tax calculation for second slab (600,001 - 1,200,000)"""
        # Minimum of second slab
        tax = TaxCalculator.calculate_annual_tax(600001)
        assert tax == 0.01  # 1% of 1 PKR
        
        # Mid-range of second slab
        tax = TaxCalculator.calculate_annual_tax(900000)
        expected = (900000 - 600000) * 0.01  # 3000
        assert tax == expected
        
        # Maximum of second slab
        tax = TaxCalculator.calculate_annual_tax(1200000)
        expected = (1200000 - 600000) * 0.01  # 6000
        assert tax == expected
    
    def test_calculate_annual_tax_slab_3(self):
        """Test tax calculation for third slab (1,200,001 - 2,200,000)"""
        # Minimum of third slab
        tax = TaxCalculator.calculate_annual_tax(1200001)
        expected = 6000 + (1 * 0.11)  # 6000.11
        assert abs(tax - expected) < 0.01
        
        # Mid-range of third slab
        tax = TaxCalculator.calculate_annual_tax(1700000)
        expected = 6000 + ((1700000 - 1200000) * 0.11)  # 61000
        assert tax == expected
        
        # Maximum of third slab
        tax = TaxCalculator.calculate_annual_tax(2200000)
        expected = 6000 + ((2200000 - 1200000) * 0.11)  # 116000
        assert tax == expected
    
    def test_calculate_annual_tax_slab_4(self):
        """Test tax calculation for fourth slab (2,200,001 - 3,200,000)"""
        # Minimum of fourth slab
        tax = TaxCalculator.calculate_annual_tax(2200001)
        expected = 116000 + (1 * 0.23)  # 116000.23
        assert abs(tax - expected) < 0.01
        
        # Maximum of fourth slab
        tax = TaxCalculator.calculate_annual_tax(3200000)
        expected = 116000 + ((3200000 - 2200000) * 0.23)  # 346000
        assert tax == expected
    
    def test_calculate_annual_tax_slab_5(self):
        """Test tax calculation for fifth slab (3,200,001 - 4,100,000)"""
        # Minimum of fifth slab
        tax = TaxCalculator.calculate_annual_tax(3200001)
        expected = 346000 + (1 * 0.30)  # 346000.30
        assert abs(tax - expected) < 0.01
        
        # Maximum of fifth slab
        tax = TaxCalculator.calculate_annual_tax(4100000)
        expected = 346000 + ((4100000 - 3200000) * 0.30)  # 616000
        assert tax == expected
    
    def test_calculate_annual_tax_slab_6(self):
        """Test tax calculation for sixth slab (Above 4,100,000)"""
        # Minimum of sixth slab
        tax = TaxCalculator.calculate_annual_tax(4100001)
        expected = 616000 + (1 * 0.35)  # 616000.35
        assert abs(tax - expected) < 0.01
        
        # High income
        tax = TaxCalculator.calculate_annual_tax(5000000)
        expected = 616000 + ((5000000 - 4100000) * 0.35)  # 931000
        assert tax == expected
    
    def test_calculate_tax_example_1(self):
        """Test complete tax calculation for example 1: 50,000 monthly"""
        result = TaxCalculator.calculate_tax(50000)
        
        assert result['annual_income'] == 600000
        assert result['base_tax'] == 0
        assert result['surcharge'] == 0
        assert result['total_tax'] == 0
        assert result['monthly_tax'] == 0
        assert result['net_monthly_salary'] == 50000
    
    def test_calculate_tax_example_2(self):
        """Test complete tax calculation for example 2: 100,000 monthly"""
        result = TaxCalculator.calculate_tax(100000)

        assert result['annual_income'] == 1200000
        assert result['base_tax'] == 6000
        assert result['surcharge'] == 0  # No surcharge as income < 10 million
        assert result['total_tax'] == 6000
        assert result['monthly_tax'] == 500  # 6000 / 12
        assert result['net_monthly_salary'] == 99500  # 100000 - 500
    
    def test_calculate_tax_example_3(self):
        """Test complete tax calculation for example 3: 350,000 monthly"""
        result = TaxCalculator.calculate_tax(350000)

        assert result['annual_income'] == 4200000
        assert result['base_tax'] == 651000  # 616000 + (100000 * 0.35)
        assert result['surcharge'] == 0  # No surcharge as income < 10 million
        assert result['total_tax'] == 651000
        # Allow small rounding differences for monthly calculations
        assert abs(result['monthly_tax'] - 54250) < 0.1  # 651000 / 12
        assert abs(result['net_monthly_salary'] - 295750) < 0.1  # 350000 - 54250

    def test_calculate_tax_with_surcharge(self):
        """Test tax calculation with surcharge (income > 10 million annually)"""
        # Monthly salary of 900,000 = 10.8 million annually (above surcharge threshold)
        result = TaxCalculator.calculate_tax(900000)

        assert result['annual_income'] == 10800000
        # Base tax calculation: 616000 + (6700000 * 0.35) = 2961000
        expected_base_tax = 616000 + ((10800000 - 4100000) * 0.35)
        assert result['base_tax'] == expected_base_tax

        # Surcharge should be 9% of base tax since income > 10 million
        expected_surcharge = expected_base_tax * 0.09
        assert result['surcharge'] == expected_surcharge

        expected_total_tax = expected_base_tax + expected_surcharge
        assert result['total_tax'] == expected_total_tax

    def test_calculate_tax_edge_cases(self):
        """Test edge cases for tax calculation"""
        # Zero income
        result = TaxCalculator.calculate_tax(0)
        assert all(value == 0 for value in result.values())
        
        # Very small income
        result = TaxCalculator.calculate_tax(0.01)
        assert result['annual_income'] == 0.12
        assert result['base_tax'] == 0
        
        # Boundary between slabs
        result = TaxCalculator.calculate_tax(50000)  # 600,000 annual
        assert result['base_tax'] == 0
        
        result = TaxCalculator.calculate_tax(50000.01)  # 600,000.12 annual
        # The tax is very small (0.0012) and gets rounded to 0.0, so let's test with a larger amount
        result = TaxCalculator.calculate_tax(50100)  # 601,200 annual
        assert result['base_tax'] > 0
    
    def test_get_tax_slab_info(self):
        """Test tax slab information retrieval"""
        # First slab
        info = TaxCalculator.get_tax_slab_info(500000)
        assert info['slab_number'] == 1
        assert info['tax_rate'] == "0.0%"
        
        # Second slab
        info = TaxCalculator.get_tax_slab_info(1000000)
        assert info['slab_number'] == 2
        assert info['tax_rate'] == "1.0%"
        
        # Last slab
        info = TaxCalculator.get_tax_slab_info(5000000)
        assert info['slab_number'] == 6
        assert info['tax_rate'] == "35.0%"
        assert info['max_income'] == "Above 4,100,000"

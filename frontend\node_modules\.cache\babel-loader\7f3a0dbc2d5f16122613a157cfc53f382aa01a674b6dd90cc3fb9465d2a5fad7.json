{"ast": null, "code": "/**\n * Tax Results Component\n * Displays the calculated tax breakdown in a user-friendly format\n */import React from'react';import{formatters}from'../services/taxApi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TaxResults=_ref=>{let{results}=_ref;if(!results){return null;}const{annual_income,base_tax,surcharge,total_tax,monthly_tax,net_monthly_salary}=results;// Calculate effective tax rate\nconst effectiveTaxRate=annual_income>0?total_tax/annual_income*100:0;const resultItems=[{label:'Annual Income',value:formatters.formatCurrency(annual_income),description:'Your total yearly income',color:'#2563eb'},{label:'Base Tax',value:formatters.formatCurrency(base_tax),description:'Tax before surcharge',color:'#dc2626'},{label:'Surcharge (9%)',value:formatters.formatCurrency(surcharge),description:'Additional surcharge on base tax',color:'#dc2626'},{label:'Total Annual Tax',value:formatters.formatCurrency(total_tax),description:'Total tax for the year',color:'#dc2626',highlight:true},{label:'Monthly Tax',value:formatters.formatCurrency(monthly_tax),description:'Tax deducted each month',color:'#dc2626',highlight:true},{label:'Net Monthly Salary',value:formatters.formatCurrency(net_monthly_salary),description:'Your take-home salary',color:'#059669',highlight:true}];return/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'24px',color:'#333',fontSize:'24px'},children:\"Tax Calculation Results\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(280px, 1fr))',gap:'16px',marginBottom:'24px'},children:resultItems.map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{style:{padding:'20px',backgroundColor:item.highlight?'#f8fafc':'#ffffff',border:item.highlight?'2px solid #e2e8f0':'1px solid #e2e8f0',borderRadius:'8px',borderLeft:`4px solid ${item.color}`},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#64748b',marginBottom:'4px',fontWeight:'500'},children:item.label}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:item.highlight?'20px':'18px',fontWeight:'700',color:item.color,marginBottom:'4px'},children:item.value}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#64748b',lineHeight:'1.4'},children:item.description})]},index))}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'20px',backgroundColor:'#f1f5f9',borderRadius:'8px',border:'1px solid #cbd5e1'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'12px',color:'#334155',fontSize:'16px',fontWeight:'600'},children:\"Tax Summary\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'16px',fontSize:'14px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#64748b'},children:\"Effective Tax Rate:\"}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:'8px',fontWeight:'600',color:'#dc2626'},children:formatters.formatPercentage(effectiveTaxRate)})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#64748b'},children:\"Monthly Deduction:\"}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:'8px',fontWeight:'600',color:'#dc2626'},children:formatters.formatPercentage(monthly_tax/(net_monthly_salary+monthly_tax)*100)})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#64748b'},children:\"Take-home Percentage:\"}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:'8px',fontWeight:'600',color:'#059669'},children:formatters.formatPercentage(net_monthly_salary/(net_monthly_salary+monthly_tax)*100)})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'20px',padding:'16px',backgroundColor:'#fef3c7',border:'1px solid #f59e0b',borderRadius:'8px',fontSize:'13px',color:'#92400e',lineHeight:'1.5'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Disclaimer:\"}),\" This calculation is based on Pakistan's income tax rules for FY 2025-26. The actual tax may vary based on additional factors such as allowances, deductions, and other income sources. Please consult a tax advisor for comprehensive tax planning.\"]})]});};export default TaxResults;", "map": {"version": 3, "names": ["React", "formatters", "jsx", "_jsx", "jsxs", "_jsxs", "TaxResults", "_ref", "results", "annual_income", "base_tax", "surcharge", "total_tax", "monthly_tax", "net_monthly_salary", "effectiveTaxRate", "resultItems", "label", "value", "formatCurrency", "description", "color", "highlight", "className", "children", "style", "marginBottom", "fontSize", "display", "gridTemplateColumns", "gap", "map", "item", "index", "padding", "backgroundColor", "border", "borderRadius", "borderLeft", "fontWeight", "lineHeight", "marginLeft", "formatPercentage", "marginTop"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/TaxResults.js"], "sourcesContent": ["/**\n * Tax Results Component\n * Displays the calculated tax breakdown in a user-friendly format\n */\nimport React from 'react';\nimport { formatters } from '../services/taxApi';\n\nconst TaxResults = ({ results }) => {\n  if (!results) {\n    return null;\n  }\n\n  const {\n    annual_income,\n    base_tax,\n    surcharge,\n    total_tax,\n    monthly_tax,\n    net_monthly_salary\n  } = results;\n\n  // Calculate effective tax rate\n  const effectiveTaxRate = annual_income > 0 ? (total_tax / annual_income) * 100 : 0;\n\n  const resultItems = [\n    {\n      label: 'Annual Income',\n      value: formatters.formatCurrency(annual_income),\n      description: 'Your total yearly income',\n      color: '#2563eb'\n    },\n    {\n      label: 'Base Tax',\n      value: formatters.formatCurrency(base_tax),\n      description: 'Tax before surcharge',\n      color: '#dc2626'\n    },\n    {\n      label: 'Surcharge (9%)',\n      value: formatters.formatCurrency(surcharge),\n      description: 'Additional surcharge on base tax',\n      color: '#dc2626'\n    },\n    {\n      label: 'Total Annual Tax',\n      value: formatters.formatCurrency(total_tax),\n      description: 'Total tax for the year',\n      color: '#dc2626',\n      highlight: true\n    },\n    {\n      label: 'Monthly Tax',\n      value: formatters.formatCurrency(monthly_tax),\n      description: 'Tax deducted each month',\n      color: '#dc2626',\n      highlight: true\n    },\n    {\n      label: 'Net Monthly Salary',\n      value: formatters.formatCurrency(net_monthly_salary),\n      description: 'Your take-home salary',\n      color: '#059669',\n      highlight: true\n    }\n  ];\n\n  return (\n    <div className=\"card\">\n      <h2 style={{ marginBottom: '24px', color: '#333', fontSize: '24px' }}>\n        Tax Calculation Results\n      </h2>\n\n      {/* Summary Cards */}\n      <div style={{ \n        display: 'grid', \n        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', \n        gap: '16px',\n        marginBottom: '24px'\n      }}>\n        {resultItems.map((item, index) => (\n          <div\n            key={index}\n            style={{\n              padding: '20px',\n              backgroundColor: item.highlight ? '#f8fafc' : '#ffffff',\n              border: item.highlight ? '2px solid #e2e8f0' : '1px solid #e2e8f0',\n              borderRadius: '8px',\n              borderLeft: `4px solid ${item.color}`\n            }}\n          >\n            <div style={{ \n              fontSize: '14px', \n              color: '#64748b', \n              marginBottom: '4px',\n              fontWeight: '500'\n            }}>\n              {item.label}\n            </div>\n            <div style={{ \n              fontSize: item.highlight ? '20px' : '18px', \n              fontWeight: '700', \n              color: item.color,\n              marginBottom: '4px'\n            }}>\n              {item.value}\n            </div>\n            <div style={{ \n              fontSize: '12px', \n              color: '#64748b',\n              lineHeight: '1.4'\n            }}>\n              {item.description}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Tax Rate Summary */}\n      <div style={{\n        padding: '20px',\n        backgroundColor: '#f1f5f9',\n        borderRadius: '8px',\n        border: '1px solid #cbd5e1'\n      }}>\n        <h3 style={{ \n          marginBottom: '12px', \n          color: '#334155', \n          fontSize: '16px',\n          fontWeight: '600'\n        }}>\n          Tax Summary\n        </h3>\n        <div style={{ \n          display: 'grid', \n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', \n          gap: '16px',\n          fontSize: '14px'\n        }}>\n          <div>\n            <span style={{ color: '#64748b' }}>Effective Tax Rate:</span>\n            <span style={{ \n              marginLeft: '8px', \n              fontWeight: '600', \n              color: '#dc2626' \n            }}>\n              {formatters.formatPercentage(effectiveTaxRate)}\n            </span>\n          </div>\n          <div>\n            <span style={{ color: '#64748b' }}>Monthly Deduction:</span>\n            <span style={{ \n              marginLeft: '8px', \n              fontWeight: '600', \n              color: '#dc2626' \n            }}>\n              {formatters.formatPercentage((monthly_tax / (net_monthly_salary + monthly_tax)) * 100)}\n            </span>\n          </div>\n          <div>\n            <span style={{ color: '#64748b' }}>Take-home Percentage:</span>\n            <span style={{ \n              marginLeft: '8px', \n              fontWeight: '600', \n              color: '#059669' \n            }}>\n              {formatters.formatPercentage((net_monthly_salary / (net_monthly_salary + monthly_tax)) * 100)}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Disclaimer */}\n      <div style={{\n        marginTop: '20px',\n        padding: '16px',\n        backgroundColor: '#fef3c7',\n        border: '1px solid #f59e0b',\n        borderRadius: '8px',\n        fontSize: '13px',\n        color: '#92400e',\n        lineHeight: '1.5'\n      }}>\n        <strong>Disclaimer:</strong> This calculation is based on Pakistan's income tax rules for FY 2025-26. \n        The actual tax may vary based on additional factors such as allowances, deductions, and other income sources. \n        Please consult a tax advisor for comprehensive tax planning.\n      </div>\n    </div>\n  );\n};\n\nexport default TaxResults;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,UAAU,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CAC7B,GAAI,CAACC,OAAO,CAAE,CACZ,MAAO,KAAI,CACb,CAEA,KAAM,CACJC,aAAa,CACbC,QAAQ,CACRC,SAAS,CACTC,SAAS,CACTC,WAAW,CACXC,kBACF,CAAC,CAAGN,OAAO,CAEX;AACA,KAAM,CAAAO,gBAAgB,CAAGN,aAAa,CAAG,CAAC,CAAIG,SAAS,CAAGH,aAAa,CAAI,GAAG,CAAG,CAAC,CAElF,KAAM,CAAAO,WAAW,CAAG,CAClB,CACEC,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAEjB,UAAU,CAACkB,cAAc,CAACV,aAAa,CAAC,CAC/CW,WAAW,CAAE,0BAA0B,CACvCC,KAAK,CAAE,SACT,CAAC,CACD,CACEJ,KAAK,CAAE,UAAU,CACjBC,KAAK,CAAEjB,UAAU,CAACkB,cAAc,CAACT,QAAQ,CAAC,CAC1CU,WAAW,CAAE,sBAAsB,CACnCC,KAAK,CAAE,SACT,CAAC,CACD,CACEJ,KAAK,CAAE,gBAAgB,CACvBC,KAAK,CAAEjB,UAAU,CAACkB,cAAc,CAACR,SAAS,CAAC,CAC3CS,WAAW,CAAE,kCAAkC,CAC/CC,KAAK,CAAE,SACT,CAAC,CACD,CACEJ,KAAK,CAAE,kBAAkB,CACzBC,KAAK,CAAEjB,UAAU,CAACkB,cAAc,CAACP,SAAS,CAAC,CAC3CQ,WAAW,CAAE,wBAAwB,CACrCC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,IACb,CAAC,CACD,CACEL,KAAK,CAAE,aAAa,CACpBC,KAAK,CAAEjB,UAAU,CAACkB,cAAc,CAACN,WAAW,CAAC,CAC7CO,WAAW,CAAE,yBAAyB,CACtCC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,IACb,CAAC,CACD,CACEL,KAAK,CAAE,oBAAoB,CAC3BC,KAAK,CAAEjB,UAAU,CAACkB,cAAc,CAACL,kBAAkB,CAAC,CACpDM,WAAW,CAAE,uBAAuB,CACpCC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,IACb,CAAC,CACF,CAED,mBACEjB,KAAA,QAAKkB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrB,IAAA,OAAIsB,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEL,KAAK,CAAE,MAAM,CAAEM,QAAQ,CAAE,MAAO,CAAE,CAAAH,QAAA,CAAC,yBAEtE,CAAI,CAAC,cAGLrB,IAAA,QAAKsB,KAAK,CAAE,CACVG,OAAO,CAAE,MAAM,CACfC,mBAAmB,CAAE,sCAAsC,CAC3DC,GAAG,CAAE,MAAM,CACXJ,YAAY,CAAE,MAChB,CAAE,CAAAF,QAAA,CACCR,WAAW,CAACe,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC3B5B,KAAA,QAEEoB,KAAK,CAAE,CACLS,OAAO,CAAE,MAAM,CACfC,eAAe,CAAEH,IAAI,CAACV,SAAS,CAAG,SAAS,CAAG,SAAS,CACvDc,MAAM,CAAEJ,IAAI,CAACV,SAAS,CAAG,mBAAmB,CAAG,mBAAmB,CAClEe,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,aAAaN,IAAI,CAACX,KAAK,EACrC,CAAE,CAAAG,QAAA,eAEFrB,IAAA,QAAKsB,KAAK,CAAE,CACVE,QAAQ,CAAE,MAAM,CAChBN,KAAK,CAAE,SAAS,CAChBK,YAAY,CAAE,KAAK,CACnBa,UAAU,CAAE,KACd,CAAE,CAAAf,QAAA,CACCQ,IAAI,CAACf,KAAK,CACR,CAAC,cACNd,IAAA,QAAKsB,KAAK,CAAE,CACVE,QAAQ,CAAEK,IAAI,CAACV,SAAS,CAAG,MAAM,CAAG,MAAM,CAC1CiB,UAAU,CAAE,KAAK,CACjBlB,KAAK,CAAEW,IAAI,CAACX,KAAK,CACjBK,YAAY,CAAE,KAChB,CAAE,CAAAF,QAAA,CACCQ,IAAI,CAACd,KAAK,CACR,CAAC,cACNf,IAAA,QAAKsB,KAAK,CAAE,CACVE,QAAQ,CAAE,MAAM,CAChBN,KAAK,CAAE,SAAS,CAChBmB,UAAU,CAAE,KACd,CAAE,CAAAhB,QAAA,CACCQ,IAAI,CAACZ,WAAW,CACd,CAAC,GA/BDa,KAgCF,CACN,CAAC,CACC,CAAC,cAGN5B,KAAA,QAAKoB,KAAK,CAAE,CACVS,OAAO,CAAE,MAAM,CACfC,eAAe,CAAE,SAAS,CAC1BE,YAAY,CAAE,KAAK,CACnBD,MAAM,CAAE,mBACV,CAAE,CAAAZ,QAAA,eACArB,IAAA,OAAIsB,KAAK,CAAE,CACTC,YAAY,CAAE,MAAM,CACpBL,KAAK,CAAE,SAAS,CAChBM,QAAQ,CAAE,MAAM,CAChBY,UAAU,CAAE,KACd,CAAE,CAAAf,QAAA,CAAC,aAEH,CAAI,CAAC,cACLnB,KAAA,QAAKoB,KAAK,CAAE,CACVG,OAAO,CAAE,MAAM,CACfC,mBAAmB,CAAE,sCAAsC,CAC3DC,GAAG,CAAE,MAAM,CACXH,QAAQ,CAAE,MACZ,CAAE,CAAAH,QAAA,eACAnB,KAAA,QAAAmB,QAAA,eACErB,IAAA,SAAMsB,KAAK,CAAE,CAAEJ,KAAK,CAAE,SAAU,CAAE,CAAAG,QAAA,CAAC,qBAAmB,CAAM,CAAC,cAC7DrB,IAAA,SAAMsB,KAAK,CAAE,CACXgB,UAAU,CAAE,KAAK,CACjBF,UAAU,CAAE,KAAK,CACjBlB,KAAK,CAAE,SACT,CAAE,CAAAG,QAAA,CACCvB,UAAU,CAACyC,gBAAgB,CAAC3B,gBAAgB,CAAC,CAC1C,CAAC,EACJ,CAAC,cACNV,KAAA,QAAAmB,QAAA,eACErB,IAAA,SAAMsB,KAAK,CAAE,CAAEJ,KAAK,CAAE,SAAU,CAAE,CAAAG,QAAA,CAAC,oBAAkB,CAAM,CAAC,cAC5DrB,IAAA,SAAMsB,KAAK,CAAE,CACXgB,UAAU,CAAE,KAAK,CACjBF,UAAU,CAAE,KAAK,CACjBlB,KAAK,CAAE,SACT,CAAE,CAAAG,QAAA,CACCvB,UAAU,CAACyC,gBAAgB,CAAE7B,WAAW,EAAIC,kBAAkB,CAAGD,WAAW,CAAC,CAAI,GAAG,CAAC,CAClF,CAAC,EACJ,CAAC,cACNR,KAAA,QAAAmB,QAAA,eACErB,IAAA,SAAMsB,KAAK,CAAE,CAAEJ,KAAK,CAAE,SAAU,CAAE,CAAAG,QAAA,CAAC,uBAAqB,CAAM,CAAC,cAC/DrB,IAAA,SAAMsB,KAAK,CAAE,CACXgB,UAAU,CAAE,KAAK,CACjBF,UAAU,CAAE,KAAK,CACjBlB,KAAK,CAAE,SACT,CAAE,CAAAG,QAAA,CACCvB,UAAU,CAACyC,gBAAgB,CAAE5B,kBAAkB,EAAIA,kBAAkB,CAAGD,WAAW,CAAC,CAAI,GAAG,CAAC,CACzF,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGNR,KAAA,QAAKoB,KAAK,CAAE,CACVkB,SAAS,CAAE,MAAM,CACjBT,OAAO,CAAE,MAAM,CACfC,eAAe,CAAE,SAAS,CAC1BC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBV,QAAQ,CAAE,MAAM,CAChBN,KAAK,CAAE,SAAS,CAChBmB,UAAU,CAAE,KACd,CAAE,CAAAhB,QAAA,eACArB,IAAA,WAAAqB,QAAA,CAAQ,aAAW,CAAQ,CAAC,uPAG9B,EAAK,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
/**
 * Tax Results Component
 * Displays the calculated tax breakdown in a user-friendly format
 */
import React from 'react';
import { formatters } from '../services/taxApi';

const TaxResults = ({ results }) => {
  if (!results) {
    return null;
  }

  const {
    annual_income,
    base_tax,
    surcharge,
    total_tax,
    monthly_tax,
    net_monthly_salary
  } = results;

  // Calculate effective tax rate
  const effectiveTaxRate = annual_income > 0 ? (total_tax / annual_income) * 100 : 0;

  const resultItems = [
    {
      label: 'Annual Income',
      value: formatters.formatCurrency(annual_income),
      description: 'Your total yearly income',
      color: '#2563eb'
    },
    {
      label: 'Base Tax',
      value: formatters.formatCurrency(base_tax),
      description: 'Tax before surcharge',
      color: '#dc2626'
    },
    {
      label: surcharge > 0 ? 'Surcharge (9%)' : 'Surcharge',
      value: formatters.formatCurrency(surcharge),
      description: surcharge > 0 ? 'Additional surcharge on base tax' : 'No surcharge (income ≤ Rs. 10 million)',
      color: surcharge > 0 ? '#dc2626' : '#64748b'
    },
    {
      label: 'Total Annual Tax',
      value: formatters.formatCurrency(total_tax),
      description: 'Total tax for the year',
      color: '#dc2626',
      highlight: true
    },
    {
      label: 'Monthly Tax',
      value: formatters.formatCurrency(monthly_tax),
      description: 'Tax deducted each month',
      color: '#dc2626',
      highlight: true
    },
    {
      label: 'Net Monthly Salary',
      value: formatters.formatCurrency(net_monthly_salary),
      description: 'Your take-home salary',
      color: '#059669',
      highlight: true
    }
  ];

  return (
    <div className="card">
      <h2 style={{ marginBottom: '24px', color: '#333', fontSize: '24px' }}>
        Tax Calculation Results
      </h2>

      {/* Summary Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
        gap: '16px',
        marginBottom: '24px'
      }}>
        {resultItems.map((item, index) => (
          <div
            key={index}
            style={{
              padding: '20px',
              backgroundColor: item.highlight ? '#f8fafc' : '#ffffff',
              border: item.highlight ? '2px solid #e2e8f0' : '1px solid #e2e8f0',
              borderRadius: '8px',
              borderLeft: `4px solid ${item.color}`
            }}
          >
            <div style={{ 
              fontSize: '14px', 
              color: '#64748b', 
              marginBottom: '4px',
              fontWeight: '500'
            }}>
              {item.label}
            </div>
            <div style={{ 
              fontSize: item.highlight ? '20px' : '18px', 
              fontWeight: '700', 
              color: item.color,
              marginBottom: '4px'
            }}>
              {item.value}
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: '#64748b',
              lineHeight: '1.4'
            }}>
              {item.description}
            </div>
          </div>
        ))}
      </div>

      {/* Tax Rate Summary */}
      <div style={{
        padding: '20px',
        backgroundColor: '#f1f5f9',
        borderRadius: '8px',
        border: '1px solid #cbd5e1'
      }}>
        <h3 style={{ 
          marginBottom: '12px', 
          color: '#334155', 
          fontSize: '16px',
          fontWeight: '600'
        }}>
          Tax Summary
        </h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '16px',
          fontSize: '14px'
        }}>
          <div>
            <span style={{ color: '#64748b' }}>Effective Tax Rate:</span>
            <span style={{ 
              marginLeft: '8px', 
              fontWeight: '600', 
              color: '#dc2626' 
            }}>
              {formatters.formatPercentage(effectiveTaxRate)}
            </span>
          </div>
          <div>
            <span style={{ color: '#64748b' }}>Monthly Deduction:</span>
            <span style={{ 
              marginLeft: '8px', 
              fontWeight: '600', 
              color: '#dc2626' 
            }}>
              {formatters.formatPercentage((monthly_tax / (net_monthly_salary + monthly_tax)) * 100)}
            </span>
          </div>
          <div>
            <span style={{ color: '#64748b' }}>Take-home Percentage:</span>
            <span style={{ 
              marginLeft: '8px', 
              fontWeight: '600', 
              color: '#059669' 
            }}>
              {formatters.formatPercentage((net_monthly_salary / (net_monthly_salary + monthly_tax)) * 100)}
            </span>
          </div>
        </div>
      </div>

      {/* Disclaimer */}
      <div style={{
        marginTop: '20px',
        padding: '16px',
        backgroundColor: '#fef3c7',
        border: '1px solid #f59e0b',
        borderRadius: '8px',
        fontSize: '13px',
        color: '#92400e',
        lineHeight: '1.5'
      }}>
        <strong>Disclaimer:</strong> This calculation is based on Pakistan's income tax rules for FY 2025-26. 
        The actual tax may vary based on additional factors such as allowances, deductions, and other income sources. 
        Please consult a tax advisor for comprehensive tax planning.
      </div>
    </div>
  );
};

export default TaxResults;

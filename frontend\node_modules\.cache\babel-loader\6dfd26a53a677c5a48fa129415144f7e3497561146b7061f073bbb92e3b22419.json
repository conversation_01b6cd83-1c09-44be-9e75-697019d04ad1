{"ast": null, "code": "/**\n * API service for Pakistan Income Tax Calculator\n * Handles all communication with the Flask backend\n */const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000/api';/**\n * Custom error class for API errors\n */class ApiError extends Error{constructor(message,status,data){super(message);this.name='ApiError';this.status=status;this.data=data;}}/**\n * Generic API request handler\n * @param {string} endpoint - API endpoint\n * @param {object} options - Fetch options\n * @returns {Promise} - API response\n */async function apiRequest(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const url=`${API_BASE_URL}${endpoint}`;const defaultOptions={headers:{'Content-Type':'application/json'}};const config={...defaultOptions,...options};try{const response=await fetch(url,config);const data=await response.json();if(!response.ok){throw new ApiError(data.error||`HTTP error! status: ${response.status}`,response.status,data);}return data;}catch(error){if(error instanceof ApiError){throw error;}// Network or other errors\nthrow new ApiError('Network error. Please check your connection and try again.',0,null);}}/**\n * Tax calculation API service\n */export const taxApi={/**\n   * Calculate tax for given monthly gross salary\n   * @param {number} monthlyGross - Monthly gross salary in PKR\n   * @returns {Promise<object>} - Tax calculation result\n   */async calculateTax(monthlyGross){const response=await apiRequest('/tax/calculate',{method:'POST',body:JSON.stringify({monthly_gross:monthlyGross})});return response.data;},/**\n   * Get calculation history\n   * @param {number} limit - Number of recent calculations to fetch\n   * @returns {Promise<Array>} - Array of calculation history\n   */async getCalculationHistory(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;const response=await apiRequest(`/tax/history?limit=${limit}`);return response.data;},/**\n   * Health check\n   * @returns {Promise<object>} - Health status\n   */async healthCheck(){return await apiRequest('/health');}};/**\n * Input validation utilities\n */export const validation={/**\n   * Validate monthly gross salary input\n   * @param {any} value - Input value to validate\n   * @returns {object} - Validation result\n   */validateMonthlyGross(value){const errors=[];// Check if value exists\nif(value===null||value===undefined||value===''){errors.push('Monthly gross salary is required');return{isValid:false,errors};}// Convert to number\nconst numValue=Number(value);// Check if it's a valid number\nif(isNaN(numValue)){errors.push('Monthly gross salary must be a valid number');return{isValid:false,errors};}// Check if it's positive\nif(numValue<0){errors.push('Monthly gross salary cannot be negative');return{isValid:false,errors};}// Check maximum limit (10 million PKR)\nif(numValue>10000000){errors.push('Monthly gross salary exceeds maximum limit (10,000,000 PKR)');return{isValid:false,errors};}return{isValid:true,errors:[]};}};/**\n * Utility functions for formatting\n */export const formatters={/**\n   * Format currency in PKR\n   * @param {number} amount - Amount to format\n   * @returns {string} - Formatted currency string\n   */formatCurrency(amount){if(typeof amount!=='number'){return'PKR 0';}return new Intl.NumberFormat('en-PK',{style:'currency',currency:'PKR',minimumFractionDigits:0,maximumFractionDigits:2}).format(amount);},/**\n   * Format number with commas\n   * @param {number} number - Number to format\n   * @returns {string} - Formatted number string\n   */formatNumber(number){if(typeof number!=='number'){return'0';}return new Intl.NumberFormat('en-PK').format(number);},/**\n   * Format percentage\n   * @param {number} value - Value to format as percentage\n   * @param {number} decimals - Number of decimal places\n   * @returns {string} - Formatted percentage string\n   */formatPercentage(value){let decimals=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;if(typeof value!=='number'){return'0%';}return`${value.toFixed(decimals)}%`;}};export default taxApi;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiError", "Error", "constructor", "message", "status", "data", "name", "apiRequest", "endpoint", "options", "arguments", "length", "undefined", "url", "defaultOptions", "headers", "config", "response", "fetch", "json", "ok", "error", "taxApi", "calculateTax", "monthlyGross", "method", "body", "JSON", "stringify", "monthly_gross", "getCalculationHistory", "limit", "healthCheck", "validation", "validateMonthlyGross", "value", "errors", "push", "<PERSON><PERSON><PERSON><PERSON>", "numValue", "Number", "isNaN", "formatters", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatNumber", "number", "formatPercentage", "decimals", "toFixed"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/services/taxApi.js"], "sourcesContent": ["/**\n * API service for Pakistan Income Tax Calculator\n * Handles all communication with the Flask backend\n */\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n/**\n * Custom error class for API errors\n */\nclass ApiError extends Error {\n  constructor(message, status, data) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n}\n\n/**\n * Generic API request handler\n * @param {string} endpoint - API endpoint\n * @param {object} options - Fetch options\n * @returns {Promise} - API response\n */\nasync function apiRequest(endpoint, options = {}) {\n  const url = `${API_BASE_URL}${endpoint}`;\n  \n  const defaultOptions = {\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  };\n  \n  const config = { ...defaultOptions, ...options };\n  \n  try {\n    const response = await fetch(url, config);\n    const data = await response.json();\n    \n    if (!response.ok) {\n      throw new ApiError(\n        data.error || `HTTP error! status: ${response.status}`,\n        response.status,\n        data\n      );\n    }\n    \n    return data;\n  } catch (error) {\n    if (error instanceof ApiError) {\n      throw error;\n    }\n    \n    // Network or other errors\n    throw new ApiError(\n      'Network error. Please check your connection and try again.',\n      0,\n      null\n    );\n  }\n}\n\n/**\n * Tax calculation API service\n */\nexport const taxApi = {\n  /**\n   * Calculate tax for given monthly gross salary\n   * @param {number} monthlyGross - Monthly gross salary in PKR\n   * @returns {Promise<object>} - Tax calculation result\n   */\n  async calculateTax(monthlyGross) {\n    const response = await apiRequest('/tax/calculate', {\n      method: 'POST',\n      body: JSON.stringify({ monthly_gross: monthlyGross }),\n    });\n    \n    return response.data;\n  },\n  \n  /**\n   * Get calculation history\n   * @param {number} limit - Number of recent calculations to fetch\n   * @returns {Promise<Array>} - Array of calculation history\n   */\n  async getCalculationHistory(limit = 10) {\n    const response = await apiRequest(`/tax/history?limit=${limit}`);\n    return response.data;\n  },\n  \n  /**\n   * Health check\n   * @returns {Promise<object>} - Health status\n   */\n  async healthCheck() {\n    return await apiRequest('/health');\n  }\n};\n\n/**\n * Input validation utilities\n */\nexport const validation = {\n  /**\n   * Validate monthly gross salary input\n   * @param {any} value - Input value to validate\n   * @returns {object} - Validation result\n   */\n  validateMonthlyGross(value) {\n    const errors = [];\n    \n    // Check if value exists\n    if (value === null || value === undefined || value === '') {\n      errors.push('Monthly gross salary is required');\n      return { isValid: false, errors };\n    }\n    \n    // Convert to number\n    const numValue = Number(value);\n    \n    // Check if it's a valid number\n    if (isNaN(numValue)) {\n      errors.push('Monthly gross salary must be a valid number');\n      return { isValid: false, errors };\n    }\n    \n    // Check if it's positive\n    if (numValue < 0) {\n      errors.push('Monthly gross salary cannot be negative');\n      return { isValid: false, errors };\n    }\n    \n    // Check maximum limit (10 million PKR)\n    if (numValue > 10000000) {\n      errors.push('Monthly gross salary exceeds maximum limit (10,000,000 PKR)');\n      return { isValid: false, errors };\n    }\n    \n    return { isValid: true, errors: [] };\n  }\n};\n\n/**\n * Utility functions for formatting\n */\nexport const formatters = {\n  /**\n   * Format currency in PKR\n   * @param {number} amount - Amount to format\n   * @returns {string} - Formatted currency string\n   */\n  formatCurrency(amount) {\n    if (typeof amount !== 'number') {\n      return 'PKR 0';\n    }\n    \n    return new Intl.NumberFormat('en-PK', {\n      style: 'currency',\n      currency: 'PKR',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 2,\n    }).format(amount);\n  },\n  \n  /**\n   * Format number with commas\n   * @param {number} number - Number to format\n   * @returns {string} - Formatted number string\n   */\n  formatNumber(number) {\n    if (typeof number !== 'number') {\n      return '0';\n    }\n    \n    return new Intl.NumberFormat('en-PK').format(number);\n  },\n  \n  /**\n   * Format percentage\n   * @param {number} value - Value to format as percentage\n   * @param {number} decimals - Number of decimal places\n   * @returns {string} - Formatted percentage string\n   */\n  formatPercentage(value, decimals = 1) {\n    if (typeof value !== 'number') {\n      return '0%';\n    }\n    \n    return `${value.toFixed(decimals)}%`;\n  }\n};\n\nexport default taxApi;\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,KAAM,CAAAA,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAEjF;AACA;AACA,GACA,KAAM,CAAAC,QAAQ,QAAS,CAAAC,KAAM,CAC3BC,WAAWA,CAACC,OAAO,CAAEC,MAAM,CAAEC,IAAI,CAAE,CACjC,KAAK,CAACF,OAAO,CAAC,CACd,IAAI,CAACG,IAAI,CAAG,UAAU,CACtB,IAAI,CAACF,MAAM,CAAGA,MAAM,CACpB,IAAI,CAACC,IAAI,CAAGA,IAAI,CAClB,CACF,CAEA;AACA;AACA;AACA;AACA;AACA,GACA,cAAe,CAAAE,UAAUA,CAACC,QAAQ,CAAgB,IAAd,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC9C,KAAM,CAAAG,GAAG,CAAG,GAAGjB,YAAY,GAAGY,QAAQ,EAAE,CAExC,KAAM,CAAAM,cAAc,CAAG,CACrBC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAED,KAAM,CAAAC,MAAM,CAAG,CAAE,GAAGF,cAAc,CAAE,GAAGL,OAAQ,CAAC,CAEhD,GAAI,CACF,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACL,GAAG,CAAEG,MAAM,CAAC,CACzC,KAAM,CAAAX,IAAI,CAAG,KAAM,CAAAY,QAAQ,CAACE,IAAI,CAAC,CAAC,CAElC,GAAI,CAACF,QAAQ,CAACG,EAAE,CAAE,CAChB,KAAM,IAAI,CAAApB,QAAQ,CAChBK,IAAI,CAACgB,KAAK,EAAI,uBAAuBJ,QAAQ,CAACb,MAAM,EAAE,CACtDa,QAAQ,CAACb,MAAM,CACfC,IACF,CAAC,CACH,CAEA,MAAO,CAAAA,IAAI,CACb,CAAE,MAAOgB,KAAK,CAAE,CACd,GAAIA,KAAK,WAAY,CAAArB,QAAQ,CAAE,CAC7B,KAAM,CAAAqB,KAAK,CACb,CAEA;AACA,KAAM,IAAI,CAAArB,QAAQ,CAChB,4DAA4D,CAC5D,CAAC,CACD,IACF,CAAC,CACH,CACF,CAEA;AACA;AACA,GACA,MAAO,MAAM,CAAAsB,MAAM,CAAG,CACpB;AACF;AACA;AACA;AACA,KACE,KAAM,CAAAC,YAAYA,CAACC,YAAY,CAAE,CAC/B,KAAM,CAAAP,QAAQ,CAAG,KAAM,CAAAV,UAAU,CAAC,gBAAgB,CAAE,CAClDkB,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEC,aAAa,CAAEL,YAAa,CAAC,CACtD,CAAC,CAAC,CAEF,MAAO,CAAAP,QAAQ,CAACZ,IAAI,CACtB,CAAC,CAED;AACF;AACA;AACA;AACA,KACE,KAAM,CAAAyB,qBAAqBA,CAAA,CAAa,IAAZ,CAAAC,KAAK,CAAArB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACpC,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAV,UAAU,CAAC,sBAAsBwB,KAAK,EAAE,CAAC,CAChE,MAAO,CAAAd,QAAQ,CAACZ,IAAI,CACtB,CAAC,CAED;AACF;AACA;AACA,KACE,KAAM,CAAA2B,WAAWA,CAAA,CAAG,CAClB,MAAO,MAAM,CAAAzB,UAAU,CAAC,SAAS,CAAC,CACpC,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAA0B,UAAU,CAAG,CACxB;AACF;AACA;AACA;AACA,KACEC,oBAAoBA,CAACC,KAAK,CAAE,CAC1B,KAAM,CAAAC,MAAM,CAAG,EAAE,CAEjB;AACA,GAAID,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKvB,SAAS,EAAIuB,KAAK,GAAK,EAAE,CAAE,CACzDC,MAAM,CAACC,IAAI,CAAC,kCAAkC,CAAC,CAC/C,MAAO,CAAEC,OAAO,CAAE,KAAK,CAAEF,MAAO,CAAC,CACnC,CAEA;AACA,KAAM,CAAAG,QAAQ,CAAGC,MAAM,CAACL,KAAK,CAAC,CAE9B;AACA,GAAIM,KAAK,CAACF,QAAQ,CAAC,CAAE,CACnBH,MAAM,CAACC,IAAI,CAAC,6CAA6C,CAAC,CAC1D,MAAO,CAAEC,OAAO,CAAE,KAAK,CAAEF,MAAO,CAAC,CACnC,CAEA;AACA,GAAIG,QAAQ,CAAG,CAAC,CAAE,CAChBH,MAAM,CAACC,IAAI,CAAC,yCAAyC,CAAC,CACtD,MAAO,CAAEC,OAAO,CAAE,KAAK,CAAEF,MAAO,CAAC,CACnC,CAEA;AACA,GAAIG,QAAQ,CAAG,QAAQ,CAAE,CACvBH,MAAM,CAACC,IAAI,CAAC,6DAA6D,CAAC,CAC1E,MAAO,CAAEC,OAAO,CAAE,KAAK,CAAEF,MAAO,CAAC,CACnC,CAEA,MAAO,CAAEE,OAAO,CAAE,IAAI,CAAEF,MAAM,CAAE,EAAG,CAAC,CACtC,CACF,CAAC,CAED;AACA;AACA,GACA,MAAO,MAAM,CAAAM,UAAU,CAAG,CACxB;AACF;AACA;AACA;AACA,KACEC,cAAcA,CAACC,MAAM,CAAE,CACrB,GAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CAC9B,MAAO,OAAO,CAChB,CAEA,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CACpCC,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,KAAK,CACfC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC,CACnB,CAAC,CAED;AACF;AACA;AACA;AACA,KACEQ,YAAYA,CAACC,MAAM,CAAE,CACnB,GAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CAC9B,MAAO,GAAG,CACZ,CAEA,MAAO,IAAI,CAAAR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACK,MAAM,CAACE,MAAM,CAAC,CACtD,CAAC,CAED;AACF;AACA;AACA;AACA;AACA,KACEC,gBAAgBA,CAACnB,KAAK,CAAgB,IAAd,CAAAoB,QAAQ,CAAA7C,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAClC,GAAI,MAAO,CAAAyB,KAAK,GAAK,QAAQ,CAAE,CAC7B,MAAO,IAAI,CACb,CAEA,MAAO,GAAGA,KAAK,CAACqB,OAAO,CAACD,QAAQ,CAAC,GAAG,CACtC,CACF,CAAC,CAED,cAAe,CAAAjC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
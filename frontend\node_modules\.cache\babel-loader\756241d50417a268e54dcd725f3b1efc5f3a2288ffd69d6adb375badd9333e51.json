{"ast": null, "code": "/**\n * Tax Calculator Form Component\n * Handles user input for monthly gross salary and form validation\n */import React,{useState}from'react';import{validation}from'../services/taxApi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TaxCalculatorForm=_ref=>{let{onCalculate,loading,error}=_ref;const[monthlyGross,setMonthlyGross]=useState('');const[validationErrors,setValidationErrors]=useState([]);const handleSubmit=e=>{e.preventDefault();// Validate input\nconst validationResult=validation.validateMonthlyGross(monthlyGross);if(!validationResult.isValid){setValidationErrors(validationResult.errors);return;}// Clear validation errors and submit\nsetValidationErrors([]);onCalculate(Number(monthlyGross));};const handleInputChange=e=>{const value=e.target.value;setMonthlyGross(value);// Clear validation errors when user starts typing\nif(validationErrors.length>0){setValidationErrors([]);}};const hasErrors=validationErrors.length>0||error;return/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'24px',color:'#333',fontSize:'24px'},children:\"Calculate Your Income Tax\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"monthlyGross\",className:\"form-label\",children:\"Monthly Gross Salary (PKR)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",id:\"monthlyGross\",className:`form-input ${hasErrors?'error':''}`,value:monthlyGross,onChange:handleInputChange,placeholder:\"Enter your monthly gross salary\",min:\"0\",max:\"10000000\",step:\"0.01\",disabled:loading}),/*#__PURE__*/_jsx(\"small\",{style:{color:'#666',fontSize:'14px',marginTop:'4px',display:'block'},children:\"Enter your monthly gross salary before any deductions\"})]}),validationErrors.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-error\",children:/*#__PURE__*/_jsx(\"ul\",{style:{margin:0,paddingLeft:'20px'},children:validationErrors.map((error,index)=>/*#__PURE__*/_jsx(\"li\",{children:error},index))})}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-error\",children:error}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading||!monthlyGross.trim(),children:[loading&&/*#__PURE__*/_jsx(\"span\",{className:\"loading\"}),loading?'Calculating...':'Calculate Tax']})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'30px',padding:'20px',backgroundColor:'#f8f9fa',borderRadius:'8px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'16px',color:'#333',fontSize:'18px'},children:\"Tax Information (FY 2025-26)\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'14px',color:'#666',lineHeight:'1.6'},children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Tax Slabs:\"})}),/*#__PURE__*/_jsxs(\"ul\",{style:{marginLeft:'20px',marginTop:'8px'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"0 \\u2013 600,000: 0%\"}),/*#__PURE__*/_jsx(\"li\",{children:\"600,001 \\u2013 1,200,000: 1%\"}),/*#__PURE__*/_jsx(\"li\",{children:\"1,200,001 \\u2013 2,200,000: 11%\"}),/*#__PURE__*/_jsx(\"li\",{children:\"2,200,001 \\u2013 3,200,000: 23%\"}),/*#__PURE__*/_jsx(\"li\",{children:\"3,200,001 \\u2013 4,100,000: 30%\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Above 4,100,000: 35%\"})]}),/*#__PURE__*/_jsxs(\"p\",{style:{marginTop:'12px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Surcharge:\"}),\" 9% of base tax\"]})]})]})]});};export default TaxCalculatorForm;", "map": {"version": 3, "names": ["React", "useState", "validation", "jsx", "_jsx", "jsxs", "_jsxs", "TaxCalculatorForm", "_ref", "onCalculate", "loading", "error", "monthlyGross", "setMonthlyGross", "validationErrors", "setValidationErrors", "handleSubmit", "e", "preventDefault", "validationResult", "validateMonthlyGross", "<PERSON><PERSON><PERSON><PERSON>", "errors", "Number", "handleInputChange", "value", "target", "length", "hasErrors", "className", "children", "style", "marginBottom", "color", "fontSize", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "min", "max", "step", "disabled", "marginTop", "display", "margin", "paddingLeft", "map", "index", "trim", "padding", "backgroundColor", "borderRadius", "lineHeight", "marginLeft"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/TaxCalculatorForm.js"], "sourcesContent": ["/**\n * Tax Calculator Form Component\n * Handles user input for monthly gross salary and form validation\n */\nimport React, { useState } from 'react';\nimport { validation } from '../services/taxApi';\n\nconst TaxCalculatorForm = ({ onCalculate, loading, error }) => {\n  const [monthlyGross, setMonthlyGross] = useState('');\n  const [validationErrors, setValidationErrors] = useState([]);\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    // Validate input\n    const validationResult = validation.validateMonthlyGross(monthlyGross);\n    \n    if (!validationResult.isValid) {\n      setValidationErrors(validationResult.errors);\n      return;\n    }\n    \n    // Clear validation errors and submit\n    setValidationErrors([]);\n    onCalculate(Number(monthlyGross));\n  };\n\n  const handleInputChange = (e) => {\n    const value = e.target.value;\n    setMonthlyGross(value);\n    \n    // Clear validation errors when user starts typing\n    if (validationErrors.length > 0) {\n      setValidationErrors([]);\n    }\n  };\n\n  const hasErrors = validationErrors.length > 0 || error;\n\n  return (\n    <div className=\"card\">\n      <h2 style={{ marginBottom: '24px', color: '#333', fontSize: '24px' }}>\n        Calculate Your Income Tax\n      </h2>\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label htmlFor=\"monthlyGross\" className=\"form-label\">\n            Monthly Gross Salary (PKR)\n          </label>\n          <input\n            type=\"number\"\n            id=\"monthlyGross\"\n            className={`form-input ${hasErrors ? 'error' : ''}`}\n            value={monthlyGross}\n            onChange={handleInputChange}\n            placeholder=\"Enter your monthly gross salary\"\n            min=\"0\"\n            max=\"10000000\"\n            step=\"0.01\"\n            disabled={loading}\n          />\n          <small style={{ color: '#666', fontSize: '14px', marginTop: '4px', display: 'block' }}>\n            Enter your monthly gross salary before any deductions\n          </small>\n        </div>\n\n        {/* Validation Errors */}\n        {validationErrors.length > 0 && (\n          <div className=\"alert alert-error\">\n            <ul style={{ margin: 0, paddingLeft: '20px' }}>\n              {validationErrors.map((error, index) => (\n                <li key={index}>{error}</li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        {/* API Error */}\n        {error && (\n          <div className=\"alert alert-error\">\n            {error}\n          </div>\n        )}\n\n        <button\n          type=\"submit\"\n          className=\"btn btn-primary\"\n          disabled={loading || !monthlyGross.trim()}\n        >\n          {loading && <span className=\"loading\"></span>}\n          {loading ? 'Calculating...' : 'Calculate Tax'}\n        </button>\n      </form>\n\n      {/* Tax Information */}\n      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n        <h3 style={{ marginBottom: '16px', color: '#333', fontSize: '18px' }}>\n          Tax Information (FY 2025-26)\n        </h3>\n        <div style={{ fontSize: '14px', color: '#666', lineHeight: '1.6' }}>\n          <p><strong>Tax Slabs:</strong></p>\n          <ul style={{ marginLeft: '20px', marginTop: '8px' }}>\n            <li>0 – 600,000: 0%</li>\n            <li>600,001 – 1,200,000: 1%</li>\n            <li>1,200,001 – 2,200,000: 11%</li>\n            <li>2,200,001 – 3,200,000: 23%</li>\n            <li>3,200,001 – 4,100,000: 30%</li>\n            <li>Above 4,100,000: 35%</li>\n          </ul>\n          <p style={{ marginTop: '12px' }}>\n            <strong>Surcharge:</strong> 9% of base tax\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TaxCalculatorForm;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,UAAU,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAAqC,IAApC,CAAEC,WAAW,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAAH,IAAA,CACxD,KAAM,CAACI,YAAY,CAAEC,eAAe,CAAC,CAAGZ,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACa,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAAAe,YAAY,CAAIC,CAAC,EAAK,CAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB;AACA,KAAM,CAAAC,gBAAgB,CAAGjB,UAAU,CAACkB,oBAAoB,CAACR,YAAY,CAAC,CAEtE,GAAI,CAACO,gBAAgB,CAACE,OAAO,CAAE,CAC7BN,mBAAmB,CAACI,gBAAgB,CAACG,MAAM,CAAC,CAC5C,OACF,CAEA;AACAP,mBAAmB,CAAC,EAAE,CAAC,CACvBN,WAAW,CAACc,MAAM,CAACX,YAAY,CAAC,CAAC,CACnC,CAAC,CAED,KAAM,CAAAY,iBAAiB,CAAIP,CAAC,EAAK,CAC/B,KAAM,CAAAQ,KAAK,CAAGR,CAAC,CAACS,MAAM,CAACD,KAAK,CAC5BZ,eAAe,CAACY,KAAK,CAAC,CAEtB;AACA,GAAIX,gBAAgB,CAACa,MAAM,CAAG,CAAC,CAAE,CAC/BZ,mBAAmB,CAAC,EAAE,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAa,SAAS,CAAGd,gBAAgB,CAACa,MAAM,CAAG,CAAC,EAAIhB,KAAK,CAEtD,mBACEL,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1B,IAAA,OAAI2B,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,2BAEtE,CAAI,CAAC,cAELxB,KAAA,SAAM6B,QAAQ,CAAEnB,YAAa,CAAAc,QAAA,eAC3BxB,KAAA,QAAKuB,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1B,IAAA,UAAOgC,OAAO,CAAC,cAAc,CAACP,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,4BAErD,CAAO,CAAC,cACR1B,IAAA,UACEiC,IAAI,CAAC,QAAQ,CACbC,EAAE,CAAC,cAAc,CACjBT,SAAS,CAAE,cAAcD,SAAS,CAAG,OAAO,CAAG,EAAE,EAAG,CACpDH,KAAK,CAAEb,YAAa,CACpB2B,QAAQ,CAAEf,iBAAkB,CAC5BgB,WAAW,CAAC,iCAAiC,CAC7CC,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,UAAU,CACdC,IAAI,CAAC,MAAM,CACXC,QAAQ,CAAElC,OAAQ,CACnB,CAAC,cACFN,IAAA,UAAO2B,KAAK,CAAE,CAAEE,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEW,SAAS,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAAAhB,QAAA,CAAC,uDAEvF,CAAO,CAAC,EACL,CAAC,CAGLhB,gBAAgB,CAACa,MAAM,CAAG,CAAC,eAC1BvB,IAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC1B,IAAA,OAAI2B,KAAK,CAAE,CAAEgB,MAAM,CAAE,CAAC,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAC3ChB,gBAAgB,CAACmC,GAAG,CAAC,CAACtC,KAAK,CAAEuC,KAAK,gBACjC9C,IAAA,OAAA0B,QAAA,CAAiBnB,KAAK,EAAbuC,KAAkB,CAC5B,CAAC,CACA,CAAC,CACF,CACN,CAGAvC,KAAK,eACJP,IAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC/BnB,KAAK,CACH,CACN,cAEDL,KAAA,WACE+B,IAAI,CAAC,QAAQ,CACbR,SAAS,CAAC,iBAAiB,CAC3Be,QAAQ,CAAElC,OAAO,EAAI,CAACE,YAAY,CAACuC,IAAI,CAAC,CAAE,CAAArB,QAAA,EAEzCpB,OAAO,eAAIN,IAAA,SAAMyB,SAAS,CAAC,SAAS,CAAO,CAAC,CAC5CnB,OAAO,CAAG,gBAAgB,CAAG,eAAe,EACvC,CAAC,EACL,CAAC,cAGPJ,KAAA,QAAKyB,KAAK,CAAE,CAAEc,SAAS,CAAE,MAAM,CAAEO,OAAO,CAAE,MAAM,CAAEC,eAAe,CAAE,SAAS,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAxB,QAAA,eAClG1B,IAAA,OAAI2B,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,8BAEtE,CAAI,CAAC,cACLxB,KAAA,QAAKyB,KAAK,CAAE,CAAEG,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAM,CAAEsB,UAAU,CAAE,KAAM,CAAE,CAAAzB,QAAA,eACjE1B,IAAA,MAAA0B,QAAA,cAAG1B,IAAA,WAAA0B,QAAA,CAAQ,YAAU,CAAQ,CAAC,CAAG,CAAC,cAClCxB,KAAA,OAAIyB,KAAK,CAAE,CAAEyB,UAAU,CAAE,MAAM,CAAEX,SAAS,CAAE,KAAM,CAAE,CAAAf,QAAA,eAClD1B,IAAA,OAAA0B,QAAA,CAAI,sBAAe,CAAI,CAAC,cACxB1B,IAAA,OAAA0B,QAAA,CAAI,8BAAuB,CAAI,CAAC,cAChC1B,IAAA,OAAA0B,QAAA,CAAI,iCAA0B,CAAI,CAAC,cACnC1B,IAAA,OAAA0B,QAAA,CAAI,iCAA0B,CAAI,CAAC,cACnC1B,IAAA,OAAA0B,QAAA,CAAI,iCAA0B,CAAI,CAAC,cACnC1B,IAAA,OAAA0B,QAAA,CAAI,sBAAoB,CAAI,CAAC,EAC3B,CAAC,cACLxB,KAAA,MAAGyB,KAAK,CAAE,CAAEc,SAAS,CAAE,MAAO,CAAE,CAAAf,QAAA,eAC9B1B,IAAA,WAAA0B,QAAA,CAAQ,YAAU,CAAQ,CAAC,kBAC7B,EAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
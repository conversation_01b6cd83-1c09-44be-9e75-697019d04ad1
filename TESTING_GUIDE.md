# Testing Guide - Pakistan Income Tax Calculator

## Overview

This guide covers testing procedures for both backend and frontend components of the Pakistan Income Tax Calculator.

## Backend Testing

### Unit Tests

The backend includes comprehensive unit tests covering:
- Tax calculation logic for all income slabs
- Input validation
- API endpoints
- Database operations
- Edge cases and boundary conditions

### Running Backend Tests

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Activate virtual environment:
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. Run all tests:
   ```bash
   python -m pytest tests/ -v
   ```

4. Run specific test files:
   ```bash
   # Test tax calculation logic only
   python -m pytest tests/test_tax_calculator.py -v
   
   # Test API endpoints only
   python -m pytest tests/test_api.py -v
   ```

5. Run tests with coverage:
   ```bash
   pip install pytest-cov
   python -m pytest tests/ --cov=app --cov-report=html
   ```

### Test Cases Covered

#### Tax Calculation Tests
- **Slab 1 (0-600,000)**: Zero tax calculation
- **Slab 2 (600,001-1,200,000)**: 1% tax calculation
- **Slab 3 (1,200,001-2,200,000)**: 11% tax calculation
- **Slab 4 (2,200,001-3,200,000)**: 23% tax calculation
- **Slab 5 (3,200,001-4,100,000)**: 30% tax calculation
- **Slab 6 (Above 4,100,000)**: 35% tax calculation
- **Edge cases**: Boundary values, very small amounts, large amounts
- **Validation**: Negative values, non-numeric inputs, excessive amounts

#### API Tests
- Health check endpoint
- Tax calculation endpoint with valid inputs
- Error handling for invalid inputs
- Database logging functionality
- Calculation history retrieval

### Expected Test Results

All tests should pass. If any tests fail:
1. Check the error messages for specific issues
2. Verify that all dependencies are installed correctly
3. Ensure the database can be created and accessed
4. Check that the tax calculation logic matches the requirements

## Manual API Testing

### Using curl (Command Line)

1. **Health Check:**
   ```bash
   curl http://localhost:5000/api/health
   ```
   Expected: `{"status": "healthy", "message": "..."}`

2. **Tax Calculation:**
   ```bash
   curl -X POST http://localhost:5000/api/tax/calculate \
     -H "Content-Type: application/json" \
     -d '{"monthly_gross": 100000}'
   ```
   Expected: Tax breakdown with all calculated fields

3. **Calculation History:**
   ```bash
   curl http://localhost:5000/api/tax/history
   ```
   Expected: Array of recent calculations

### Using PowerShell (Windows)

1. **Health Check:**
   ```powershell
   (Invoke-WebRequest -Uri "http://localhost:5000/api/health" -Method GET).Content
   ```

2. **Tax Calculation:**
   ```powershell
   $body = '{"monthly_gross": 100000}'
   (Invoke-WebRequest -Uri "http://localhost:5000/api/tax/calculate" -Method POST -Body $body -ContentType "application/json").Content
   ```

### Test Scenarios

#### Valid Inputs
- Monthly salary: 50,000 (no tax)
- Monthly salary: 100,000 (second slab)
- Monthly salary: 200,000 (third slab)
- Monthly salary: 350,000 (sixth slab)

#### Invalid Inputs
- Negative salary: -50,000
- Non-numeric: "abc"
- Excessive amount: 15,000,000
- Missing field: {}

## Frontend Testing

### Unit Tests

1. Navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. Run React tests:
   ```bash
   npm test
   ```

### Manual Frontend Testing

1. **Start the application:**
   ```bash
   npm start
   ```

2. **Test scenarios:**
   - Enter valid salary amounts
   - Submit form and verify results
   - Test input validation
   - Test responsive design on different screen sizes
   - Test error handling when backend is unavailable

### Browser Testing

Test the application in different browsers:
- Chrome
- Firefox
- Safari
- Edge

Test responsive design:
- Desktop (1920x1080)
- Tablet (768x1024)
- Mobile (375x667)

## Integration Testing

### Full Stack Testing

1. **Start both servers:**
   ```bash
   # Terminal 1: Backend
   cd backend
   venv\Scripts\activate
   python run.py
   
   # Terminal 2: Frontend
   cd frontend
   npm start
   ```

2. **Test complete workflow:**
   - Open http://localhost:3000
   - Enter a salary amount
   - Submit the form
   - Verify the calculation results
   - Check that the calculation is logged in the backend

### API Integration

1. **Test API connectivity:**
   - Verify frontend can reach backend
   - Test error handling when backend is down
   - Test CORS functionality

2. **Test data flow:**
   - Input validation on frontend
   - API request/response handling
   - Result display formatting
   - Error message display

## Performance Testing

### Backend Performance

1. **Load testing with multiple requests:**
   ```bash
   # Install Apache Bench (if available)
   ab -n 1000 -c 10 http://localhost:5000/api/health
   ```

2. **Database performance:**
   - Test with large number of logged calculations
   - Verify query performance for history endpoint

### Frontend Performance

1. **Page load time:**
   - Measure initial load time
   - Test with slow network conditions

2. **Responsiveness:**
   - Test form submission speed
   - Verify smooth animations and transitions

## Troubleshooting Common Issues

### Backend Issues

1. **Import errors:**
   - Verify virtual environment is activated
   - Check all dependencies are installed
   - Ensure Python path is correct

2. **Database errors:**
   - Check file permissions
   - Verify SQLite is available
   - Clear database file if corrupted

3. **Port conflicts:**
   - Ensure port 5000 is available
   - Check for other Flask applications running

### Frontend Issues

1. **npm install failures:**
   - Clear npm cache: `npm cache clean --force`
   - Delete node_modules and reinstall
   - Check Node.js version compatibility

2. **API connection errors:**
   - Verify backend is running
   - Check CORS configuration
   - Verify proxy settings in package.json

3. **Build errors:**
   - Check for syntax errors in React components
   - Verify all imports are correct
   - Check for missing dependencies

## Continuous Integration

For production deployment, consider setting up:
- Automated testing on code commits
- Code coverage reporting
- Performance monitoring
- Security scanning
- Dependency vulnerability checks

## Test Data

### Sample Test Cases

| Monthly Gross | Expected Annual | Expected Base Tax | Expected Total Tax | Surcharge Applied |
|---------------|----------------|-------------------|-------------------|-------------------|
| 0 | 0 | 0 | 0 | No |
| 50,000 | 600,000 | 0 | 0 | No |
| 100,000 | 1,200,000 | 6,000 | 6,000 | No |
| 200,000 | 2,400,000 | 146,000 | 146,000 | No |
| 350,000 | 4,200,000 | 651,000 | 651,000 | No |
| 900,000 | 10,800,000 | 2,961,000 | 3,227,490 | Yes (9%) |

Use these test cases to verify calculations are correct across all components.

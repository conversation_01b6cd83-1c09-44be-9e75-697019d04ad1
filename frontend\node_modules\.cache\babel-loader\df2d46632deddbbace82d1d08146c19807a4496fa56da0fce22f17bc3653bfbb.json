{"ast": null, "code": "var _jsxFileName = \"C:\\\\Hamza\\\\my-tax-calculator\\\\frontend\\\\src\\\\components\\\\TaxCalculatorForm.js\",\n  _s = $RefreshSig$();\n/**\n * Tax Calculator Form Component\n * Handles user input for monthly gross salary and form validation\n */\nimport React, { useState } from 'react';\nimport { validation } from '../services/taxApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TaxCalculatorForm = ({\n  onCalculate,\n  loading,\n  error\n}) => {\n  _s();\n  const [monthlyGross, setMonthlyGross] = useState('');\n  const [validationErrors, setValidationErrors] = useState([]);\n  const handleSubmit = e => {\n    e.preventDefault();\n\n    // Validate input\n    const validationResult = validation.validateMonthlyGross(monthlyGross);\n    if (!validationResult.isValid) {\n      setValidationErrors(validationResult.errors);\n      return;\n    }\n\n    // Clear validation errors and submit\n    setValidationErrors([]);\n    onCalculate(Number(monthlyGross));\n  };\n  const handleInputChange = e => {\n    const value = e.target.value;\n    setMonthlyGross(value);\n\n    // Clear validation errors when user starts typing\n    if (validationErrors.length > 0) {\n      setValidationErrors([]);\n    }\n  };\n  const hasErrors = validationErrors.length > 0 || error;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        marginBottom: '24px',\n        color: '#333',\n        fontSize: '24px'\n      },\n      children: \"Calculate Your Income Tax\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"monthlyGross\",\n          className: \"form-label\",\n          children: \"Monthly Gross Salary (PKR)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          id: \"monthlyGross\",\n          className: `form-input ${hasErrors ? 'error' : ''}`,\n          value: monthlyGross,\n          onChange: handleInputChange,\n          placeholder: \"Enter your monthly gross salary\",\n          min: \"0\",\n          max: \"10000000\",\n          step: \"0.01\",\n          disabled: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666',\n            fontSize: '14px',\n            marginTop: '4px',\n            display: 'block'\n          },\n          children: \"Enter your monthly gross salary before any deductions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), validationErrors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-error\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: 0,\n            paddingLeft: '20px'\n          },\n          children: validationErrors.map((error, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: error\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"btn btn-primary\",\n        disabled: loading || !monthlyGross.trim(),\n        children: [loading && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"loading\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 23\n        }, this), loading ? 'Calculating...' : 'Calculate Tax']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '30px',\n        padding: '20px',\n        backgroundColor: '#f8f9fa',\n        borderRadius: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '16px',\n          color: '#333',\n          fontSize: '18px'\n        },\n        children: \"Tax Information (FY 2025-26)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          color: '#666',\n          lineHeight: '1.6'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Tax Slabs:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 14\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            marginLeft: '20px',\n            marginTop: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"0 \\u2013 600,000: 0%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"600,001 \\u2013 1,200,000: 1%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"1,200,001 \\u2013 2,200,000: 11%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"2,200,001 \\u2013 3,200,000: 23%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"3,200,001 \\u2013 4,100,000: 30%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Above 4,100,000: 35%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginTop: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Surcharge:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), \" 9% of base tax\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(TaxCalculatorForm, \"F+oAhNxmm4OuEQLoJeIxNhvY1sM=\");\n_c = TaxCalculatorForm;\nexport default TaxCalculatorForm;\nvar _c;\n$RefreshReg$(_c, \"TaxCalculatorForm\");", "map": {"version": 3, "names": ["React", "useState", "validation", "jsxDEV", "_jsxDEV", "TaxCalculatorForm", "onCalculate", "loading", "error", "_s", "monthlyGross", "setMonthlyGross", "validationErrors", "setValidationErrors", "handleSubmit", "e", "preventDefault", "validationResult", "validateMonthlyGross", "<PERSON><PERSON><PERSON><PERSON>", "errors", "Number", "handleInputChange", "value", "target", "length", "hasErrors", "className", "children", "style", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "min", "max", "step", "disabled", "marginTop", "display", "margin", "paddingLeft", "map", "index", "trim", "padding", "backgroundColor", "borderRadius", "lineHeight", "marginLeft", "_c", "$RefreshReg$"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/TaxCalculatorForm.js"], "sourcesContent": ["/**\n * Tax Calculator Form Component\n * Handles user input for monthly gross salary and form validation\n */\nimport React, { useState } from 'react';\nimport { validation } from '../services/taxApi';\n\nconst TaxCalculatorForm = ({ onCalculate, loading, error }) => {\n  const [monthlyGross, setMonthlyGross] = useState('');\n  const [validationErrors, setValidationErrors] = useState([]);\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    // Validate input\n    const validationResult = validation.validateMonthlyGross(monthlyGross);\n    \n    if (!validationResult.isValid) {\n      setValidationErrors(validationResult.errors);\n      return;\n    }\n    \n    // Clear validation errors and submit\n    setValidationErrors([]);\n    onCalculate(Number(monthlyGross));\n  };\n\n  const handleInputChange = (e) => {\n    const value = e.target.value;\n    setMonthlyGross(value);\n    \n    // Clear validation errors when user starts typing\n    if (validationErrors.length > 0) {\n      setValidationErrors([]);\n    }\n  };\n\n  const hasErrors = validationErrors.length > 0 || error;\n\n  return (\n    <div className=\"card\">\n      <h2 style={{ marginBottom: '24px', color: '#333', fontSize: '24px' }}>\n        Calculate Your Income Tax\n      </h2>\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label htmlFor=\"monthlyGross\" className=\"form-label\">\n            Monthly Gross Salary (PKR)\n          </label>\n          <input\n            type=\"number\"\n            id=\"monthlyGross\"\n            className={`form-input ${hasErrors ? 'error' : ''}`}\n            value={monthlyGross}\n            onChange={handleInputChange}\n            placeholder=\"Enter your monthly gross salary\"\n            min=\"0\"\n            max=\"10000000\"\n            step=\"0.01\"\n            disabled={loading}\n          />\n          <small style={{ color: '#666', fontSize: '14px', marginTop: '4px', display: 'block' }}>\n            Enter your monthly gross salary before any deductions\n          </small>\n        </div>\n\n        {/* Validation Errors */}\n        {validationErrors.length > 0 && (\n          <div className=\"alert alert-error\">\n            <ul style={{ margin: 0, paddingLeft: '20px' }}>\n              {validationErrors.map((error, index) => (\n                <li key={index}>{error}</li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        {/* API Error */}\n        {error && (\n          <div className=\"alert alert-error\">\n            {error}\n          </div>\n        )}\n\n        <button\n          type=\"submit\"\n          className=\"btn btn-primary\"\n          disabled={loading || !monthlyGross.trim()}\n        >\n          {loading && <span className=\"loading\"></span>}\n          {loading ? 'Calculating...' : 'Calculate Tax'}\n        </button>\n      </form>\n\n      {/* Tax Information */}\n      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n        <h3 style={{ marginBottom: '16px', color: '#333', fontSize: '18px' }}>\n          Tax Information (FY 2025-26)\n        </h3>\n        <div style={{ fontSize: '14px', color: '#666', lineHeight: '1.6' }}>\n          <p><strong>Tax Slabs:</strong></p>\n          <ul style={{ marginLeft: '20px', marginTop: '8px' }}>\n            <li>0 – 600,000: 0%</li>\n            <li>600,001 – 1,200,000: 1%</li>\n            <li>1,200,001 – 2,200,000: 11%</li>\n            <li>2,200,001 – 3,200,000: 23%</li>\n            <li>3,200,001 – 4,100,000: 30%</li>\n            <li>Above 4,100,000: 35%</li>\n          </ul>\n          <p style={{ marginTop: '12px' }}>\n            <strong>Surcharge:</strong> 9% of base tax\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TaxCalculatorForm;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,OAAO;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAMa,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,gBAAgB,GAAGf,UAAU,CAACgB,oBAAoB,CAACR,YAAY,CAAC;IAEtE,IAAI,CAACO,gBAAgB,CAACE,OAAO,EAAE;MAC7BN,mBAAmB,CAACI,gBAAgB,CAACG,MAAM,CAAC;MAC5C;IACF;;IAEA;IACAP,mBAAmB,CAAC,EAAE,CAAC;IACvBP,WAAW,CAACe,MAAM,CAACX,YAAY,CAAC,CAAC;EACnC,CAAC;EAED,MAAMY,iBAAiB,GAAIP,CAAC,IAAK;IAC/B,MAAMQ,KAAK,GAAGR,CAAC,CAACS,MAAM,CAACD,KAAK;IAC5BZ,eAAe,CAACY,KAAK,CAAC;;IAEtB;IACA,IAAIX,gBAAgB,CAACa,MAAM,GAAG,CAAC,EAAE;MAC/BZ,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAMa,SAAS,GAAGd,gBAAgB,CAACa,MAAM,GAAG,CAAC,IAAIjB,KAAK;EAEtD,oBACEJ,OAAA;IAAKuB,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBxB,OAAA;MAAIyB,KAAK,EAAE;QAAEC,YAAY,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAJ,QAAA,EAAC;IAEtE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELhC,OAAA;MAAMiC,QAAQ,EAAEvB,YAAa;MAAAc,QAAA,gBAC3BxB,OAAA;QAAKuB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxB,OAAA;UAAOkC,OAAO,EAAC,cAAc;UAACX,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAErD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRhC,OAAA;UACEmC,IAAI,EAAC,QAAQ;UACbC,EAAE,EAAC,cAAc;UACjBb,SAAS,EAAE,cAAcD,SAAS,GAAG,OAAO,GAAG,EAAE,EAAG;UACpDH,KAAK,EAAEb,YAAa;UACpB+B,QAAQ,EAAEnB,iBAAkB;UAC5BoB,WAAW,EAAC,iCAAiC;UAC7CC,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,UAAU;UACdC,IAAI,EAAC,MAAM;UACXC,QAAQ,EAAEvC;QAAQ;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACFhC,OAAA;UAAOyB,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,MAAM;YAAEe,SAAS,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAQ,CAAE;UAAApB,QAAA,EAAC;QAEvF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGLxB,gBAAgB,CAACa,MAAM,GAAG,CAAC,iBAC1BrB,OAAA;QAAKuB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCxB,OAAA;UAAIyB,KAAK,EAAE;YAAEoB,MAAM,EAAE,CAAC;YAAEC,WAAW,EAAE;UAAO,CAAE;UAAAtB,QAAA,EAC3ChB,gBAAgB,CAACuC,GAAG,CAAC,CAAC3C,KAAK,EAAE4C,KAAK,kBACjChD,OAAA;YAAAwB,QAAA,EAAiBpB;UAAK,GAAb4C,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,EAGA5B,KAAK,iBACJJ,OAAA;QAAKuB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/BpB;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDhC,OAAA;QACEmC,IAAI,EAAC,QAAQ;QACbZ,SAAS,EAAC,iBAAiB;QAC3BmB,QAAQ,EAAEvC,OAAO,IAAI,CAACG,YAAY,CAAC2C,IAAI,CAAC,CAAE;QAAAzB,QAAA,GAEzCrB,OAAO,iBAAIH,OAAA;UAAMuB,SAAS,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5C7B,OAAO,GAAG,gBAAgB,GAAG,eAAe;MAAA;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPhC,OAAA;MAAKyB,KAAK,EAAE;QAAEkB,SAAS,EAAE,MAAM;QAAEO,OAAO,EAAE,MAAM;QAAEC,eAAe,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAM,CAAE;MAAA5B,QAAA,gBAClGxB,OAAA;QAAIyB,KAAK,EAAE;UAAEC,YAAY,EAAE,MAAM;UAAEC,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEtE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLhC,OAAA;QAAKyB,KAAK,EAAE;UAAEG,QAAQ,EAAE,MAAM;UAAED,KAAK,EAAE,MAAM;UAAE0B,UAAU,EAAE;QAAM,CAAE;QAAA7B,QAAA,gBACjExB,OAAA;UAAAwB,QAAA,eAAGxB,OAAA;YAAAwB,QAAA,EAAQ;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClChC,OAAA;UAAIyB,KAAK,EAAE;YAAE6B,UAAU,EAAE,MAAM;YAAEX,SAAS,EAAE;UAAM,CAAE;UAAAnB,QAAA,gBAClDxB,OAAA;YAAAwB,QAAA,EAAI;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBhC,OAAA;YAAAwB,QAAA,EAAI;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChChC,OAAA;YAAAwB,QAAA,EAAI;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnChC,OAAA;YAAAwB,QAAA,EAAI;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnChC,OAAA;YAAAwB,QAAA,EAAI;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnChC,OAAA;YAAAwB,QAAA,EAAI;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLhC,OAAA;UAAGyB,KAAK,EAAE;YAAEkB,SAAS,EAAE;UAAO,CAAE;UAAAnB,QAAA,gBAC9BxB,OAAA;YAAAwB,QAAA,EAAQ;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,mBAC7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA9GIJ,iBAAiB;AAAAsD,EAAA,GAAjBtD,iBAAiB;AAgHvB,eAAeA,iBAAiB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
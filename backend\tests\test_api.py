"""
API endpoint tests for Pakistan Income Tax Calculator
"""
import pytest
import json
from app import create_app, db
from app.models import TaxCalculationLog

@pytest.fixture
def app():
    """Create test app instance"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

class TestHealthEndpoint:
    """Test health check endpoint"""
    
    def test_health_check(self, client):
        """Test health check endpoint returns 200"""
        response = client.get('/api/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'Pakistan Income Tax Calculator' in data['message']

class TestTaxCalculationEndpoint:
    """Test tax calculation endpoint"""
    
    def test_calculate_tax_valid_input(self, client):
        """Test tax calculation with valid input"""
        payload = {'monthly_gross': 100000}
        response = client.post('/api/tax/calculate', 
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        
        result = data['data']
        assert result['annual_income'] == 1200000
        assert result['base_tax'] == 6000
        assert result['surcharge'] == 0  # No surcharge as income < 10 million
        assert result['total_tax'] == 6000
        assert result['monthly_tax'] == 500
        assert result['net_monthly_salary'] == 99500
    
    def test_calculate_tax_zero_income(self, client):
        """Test tax calculation with zero income"""
        payload = {'monthly_gross': 0}
        response = client.post('/api/tax/calculate',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        
        result = data['data']
        assert result['annual_income'] == 0
        assert result['base_tax'] == 0
        assert result['surcharge'] == 0
        assert result['total_tax'] == 0
        assert result['monthly_tax'] == 0
        assert result['net_monthly_salary'] == 0
    
    def test_calculate_tax_high_income(self, client):
        """Test tax calculation with high income"""
        payload = {'monthly_gross': 500000}
        response = client.post('/api/tax/calculate',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        
        result = data['data']
        assert result['annual_income'] == 6000000
        # High income should have significant tax
        assert result['base_tax'] > 1000000
        assert result['surcharge'] > 0
        assert result['monthly_tax'] > 0
    
    def test_calculate_tax_missing_body(self, client):
        """Test tax calculation with missing request body"""
        response = client.post('/api/tax/calculate',
                             content_type='application/json')
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Request body is required' in data['error']
    
    def test_calculate_tax_missing_field(self, client):
        """Test tax calculation with missing monthly_gross field"""
        payload = {'salary': 100000}  # Wrong field name
        response = client.post('/api/tax/calculate',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'monthly_gross field is required' in data['error']
    
    def test_calculate_tax_negative_income(self, client):
        """Test tax calculation with negative income"""
        payload = {'monthly_gross': -50000}
        response = client.post('/api/tax/calculate',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'cannot be negative' in data['error']
    
    def test_calculate_tax_invalid_type(self, client):
        """Test tax calculation with invalid data type"""
        payload = {'monthly_gross': 'not_a_number'}
        response = client.post('/api/tax/calculate',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'must be a number' in data['error']
    
    def test_calculate_tax_excessive_income(self, client):
        """Test tax calculation with excessive income"""
        payload = {'monthly_gross': 15000000}  # 15 million PKR
        response = client.post('/api/tax/calculate',
                             data=json.dumps(payload),
                             content_type='application/json')
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'exceeds maximum limit' in data['error']

class TestCalculationHistory:
    """Test calculation history endpoint"""
    
    def test_get_empty_history(self, client):
        """Test getting history when no calculations exist"""
        response = client.get('/api/tax/history')
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data'] == []
    
    def test_get_history_after_calculation(self, client, app):
        """Test getting history after performing calculations"""
        # Perform a calculation first
        payload = {'monthly_gross': 100000}
        calc_response = client.post('/api/tax/calculate',
                                  data=json.dumps(payload),
                                  content_type='application/json')
        assert calc_response.status_code == 200
        
        # Get history
        response = client.get('/api/tax/history')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']) == 1
        
        history_item = data['data'][0]
        assert history_item['monthly_gross'] == 100000
        assert history_item['annual_income'] == 1200000
        assert 'timestamp' in history_item
        assert 'id' in history_item
    
    def test_get_history_with_limit(self, client):
        """Test getting history with custom limit"""
        # Perform multiple calculations
        for salary in [50000, 75000, 100000, 125000, 150000]:
            payload = {'monthly_gross': salary}
            client.post('/api/tax/calculate',
                       data=json.dumps(payload),
                       content_type='application/json')
        
        # Get history with limit
        response = client.get('/api/tax/history?limit=3')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']) == 3
        
        # Should be in descending order (most recent first)
        salaries = [item['monthly_gross'] for item in data['data']]
        assert salaries == [150000, 125000, 100000]

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Hamza\\\\my-tax-calculator\\\\frontend\\\\src\\\\components\\\\TaxResults.js\";\n/**\n * Tax Results Component\n * Displays the calculated tax breakdown in a user-friendly format\n */\nimport React from 'react';\nimport { formatters } from '../services/taxApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TaxResults = ({\n  results\n}) => {\n  if (!results) {\n    return null;\n  }\n  const {\n    annual_income,\n    base_tax,\n    surcharge,\n    total_tax,\n    monthly_tax,\n    net_monthly_salary\n  } = results;\n\n  // Calculate effective tax rate\n  const effectiveTaxRate = annual_income > 0 ? total_tax / annual_income * 100 : 0;\n  const resultItems = [{\n    label: 'Annual Income',\n    value: formatters.formatCurrency(annual_income),\n    description: 'Your total yearly income',\n    color: '#2563eb'\n  }, {\n    label: 'Base Tax',\n    value: formatters.formatCurrency(base_tax),\n    description: 'Tax before surcharge',\n    color: '#dc2626'\n  }, {\n    label: 'Surcharge (9%)',\n    value: formatters.formatCurrency(surcharge),\n    description: 'Additional surcharge on base tax',\n    color: '#dc2626'\n  }, {\n    label: 'Total Annual Tax',\n    value: formatters.formatCurrency(total_tax),\n    description: 'Total tax for the year',\n    color: '#dc2626',\n    highlight: true\n  }, {\n    label: 'Monthly Tax',\n    value: formatters.formatCurrency(monthly_tax),\n    description: 'Tax deducted each month',\n    color: '#dc2626',\n    highlight: true\n  }, {\n    label: 'Net Monthly Salary',\n    value: formatters.formatCurrency(net_monthly_salary),\n    description: 'Your take-home salary',\n    color: '#059669',\n    highlight: true\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        marginBottom: '24px',\n        color: '#333',\n        fontSize: '24px'\n      },\n      children: \"Tax Calculation Results\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n        gap: '16px',\n        marginBottom: '24px'\n      },\n      children: resultItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px',\n          backgroundColor: item.highlight ? '#f8fafc' : '#ffffff',\n          border: item.highlight ? '2px solid #e2e8f0' : '1px solid #e2e8f0',\n          borderRadius: '8px',\n          borderLeft: `4px solid ${item.color}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#64748b',\n            marginBottom: '4px',\n            fontWeight: '500'\n          },\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: item.highlight ? '20px' : '18px',\n            fontWeight: '700',\n            color: item.color,\n            marginBottom: '4px'\n          },\n          children: item.value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#64748b',\n            lineHeight: '1.4'\n          },\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '20px',\n        backgroundColor: '#f1f5f9',\n        borderRadius: '8px',\n        border: '1px solid #cbd5e1'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: '12px',\n          color: '#334155',\n          fontSize: '16px',\n          fontWeight: '600'\n        },\n        children: \"Tax Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '16px',\n          fontSize: '14px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#64748b'\n            },\n            children: \"Effective Tax Rate:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '8px',\n              fontWeight: '600',\n              color: '#dc2626'\n            },\n            children: formatters.formatPercentage(effectiveTaxRate)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#64748b'\n            },\n            children: \"Monthly Deduction:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '8px',\n              fontWeight: '600',\n              color: '#dc2626'\n            },\n            children: formatters.formatPercentage(monthly_tax / (net_monthly_salary + monthly_tax) * 100)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#64748b'\n            },\n            children: \"Take-home Percentage:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '8px',\n              fontWeight: '600',\n              color: '#059669'\n            },\n            children: formatters.formatPercentage(net_monthly_salary / (net_monthly_salary + monthly_tax) * 100)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '20px',\n        padding: '16px',\n        backgroundColor: '#fef3c7',\n        border: '1px solid #f59e0b',\n        borderRadius: '8px',\n        fontSize: '13px',\n        color: '#92400e',\n        lineHeight: '1.5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Disclaimer:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), \" This calculation is based on Pakistan's income tax rules for FY 2025-26. The actual tax may vary based on additional factors such as allowances, deductions, and other income sources. Please consult a tax advisor for comprehensive tax planning.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_c = TaxResults;\nexport default TaxResults;\nvar _c;\n$RefreshReg$(_c, \"TaxResults\");", "map": {"version": 3, "names": ["React", "formatters", "jsxDEV", "_jsxDEV", "TaxResults", "results", "annual_income", "base_tax", "surcharge", "total_tax", "monthly_tax", "net_monthly_salary", "effectiveTaxRate", "resultItems", "label", "value", "formatCurrency", "description", "color", "highlight", "className", "children", "style", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gridTemplateColumns", "gap", "map", "item", "index", "padding", "backgroundColor", "border", "borderRadius", "borderLeft", "fontWeight", "lineHeight", "marginLeft", "formatPercentage", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/TaxResults.js"], "sourcesContent": ["/**\n * Tax Results Component\n * Displays the calculated tax breakdown in a user-friendly format\n */\nimport React from 'react';\nimport { formatters } from '../services/taxApi';\n\nconst TaxResults = ({ results }) => {\n  if (!results) {\n    return null;\n  }\n\n  const {\n    annual_income,\n    base_tax,\n    surcharge,\n    total_tax,\n    monthly_tax,\n    net_monthly_salary\n  } = results;\n\n  // Calculate effective tax rate\n  const effectiveTaxRate = annual_income > 0 ? (total_tax / annual_income) * 100 : 0;\n\n  const resultItems = [\n    {\n      label: 'Annual Income',\n      value: formatters.formatCurrency(annual_income),\n      description: 'Your total yearly income',\n      color: '#2563eb'\n    },\n    {\n      label: 'Base Tax',\n      value: formatters.formatCurrency(base_tax),\n      description: 'Tax before surcharge',\n      color: '#dc2626'\n    },\n    {\n      label: 'Surcharge (9%)',\n      value: formatters.formatCurrency(surcharge),\n      description: 'Additional surcharge on base tax',\n      color: '#dc2626'\n    },\n    {\n      label: 'Total Annual Tax',\n      value: formatters.formatCurrency(total_tax),\n      description: 'Total tax for the year',\n      color: '#dc2626',\n      highlight: true\n    },\n    {\n      label: 'Monthly Tax',\n      value: formatters.formatCurrency(monthly_tax),\n      description: 'Tax deducted each month',\n      color: '#dc2626',\n      highlight: true\n    },\n    {\n      label: 'Net Monthly Salary',\n      value: formatters.formatCurrency(net_monthly_salary),\n      description: 'Your take-home salary',\n      color: '#059669',\n      highlight: true\n    }\n  ];\n\n  return (\n    <div className=\"card\">\n      <h2 style={{ marginBottom: '24px', color: '#333', fontSize: '24px' }}>\n        Tax Calculation Results\n      </h2>\n\n      {/* Summary Cards */}\n      <div style={{ \n        display: 'grid', \n        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', \n        gap: '16px',\n        marginBottom: '24px'\n      }}>\n        {resultItems.map((item, index) => (\n          <div\n            key={index}\n            style={{\n              padding: '20px',\n              backgroundColor: item.highlight ? '#f8fafc' : '#ffffff',\n              border: item.highlight ? '2px solid #e2e8f0' : '1px solid #e2e8f0',\n              borderRadius: '8px',\n              borderLeft: `4px solid ${item.color}`\n            }}\n          >\n            <div style={{ \n              fontSize: '14px', \n              color: '#64748b', \n              marginBottom: '4px',\n              fontWeight: '500'\n            }}>\n              {item.label}\n            </div>\n            <div style={{ \n              fontSize: item.highlight ? '20px' : '18px', \n              fontWeight: '700', \n              color: item.color,\n              marginBottom: '4px'\n            }}>\n              {item.value}\n            </div>\n            <div style={{ \n              fontSize: '12px', \n              color: '#64748b',\n              lineHeight: '1.4'\n            }}>\n              {item.description}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Tax Rate Summary */}\n      <div style={{\n        padding: '20px',\n        backgroundColor: '#f1f5f9',\n        borderRadius: '8px',\n        border: '1px solid #cbd5e1'\n      }}>\n        <h3 style={{ \n          marginBottom: '12px', \n          color: '#334155', \n          fontSize: '16px',\n          fontWeight: '600'\n        }}>\n          Tax Summary\n        </h3>\n        <div style={{ \n          display: 'grid', \n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', \n          gap: '16px',\n          fontSize: '14px'\n        }}>\n          <div>\n            <span style={{ color: '#64748b' }}>Effective Tax Rate:</span>\n            <span style={{ \n              marginLeft: '8px', \n              fontWeight: '600', \n              color: '#dc2626' \n            }}>\n              {formatters.formatPercentage(effectiveTaxRate)}\n            </span>\n          </div>\n          <div>\n            <span style={{ color: '#64748b' }}>Monthly Deduction:</span>\n            <span style={{ \n              marginLeft: '8px', \n              fontWeight: '600', \n              color: '#dc2626' \n            }}>\n              {formatters.formatPercentage((monthly_tax / (net_monthly_salary + monthly_tax)) * 100)}\n            </span>\n          </div>\n          <div>\n            <span style={{ color: '#64748b' }}>Take-home Percentage:</span>\n            <span style={{ \n              marginLeft: '8px', \n              fontWeight: '600', \n              color: '#059669' \n            }}>\n              {formatters.formatPercentage((net_monthly_salary / (net_monthly_salary + monthly_tax)) * 100)}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Disclaimer */}\n      <div style={{\n        marginTop: '20px',\n        padding: '16px',\n        backgroundColor: '#fef3c7',\n        border: '1px solid #f59e0b',\n        borderRadius: '8px',\n        fontSize: '13px',\n        color: '#92400e',\n        lineHeight: '1.5'\n      }}>\n        <strong>Disclaimer:</strong> This calculation is based on Pakistan's income tax rules for FY 2025-26. \n        The actual tax may vary based on additional factors such as allowances, deductions, and other income sources. \n        Please consult a tax advisor for comprehensive tax planning.\n      </div>\n    </div>\n  );\n};\n\nexport default TaxResults;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAClC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,MAAM;IACJC,aAAa;IACbC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC;EACF,CAAC,GAAGN,OAAO;;EAEX;EACA,MAAMO,gBAAgB,GAAGN,aAAa,GAAG,CAAC,GAAIG,SAAS,GAAGH,aAAa,GAAI,GAAG,GAAG,CAAC;EAElF,MAAMO,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAEd,UAAU,CAACe,cAAc,CAACV,aAAa,CAAC;IAC/CW,WAAW,EAAE,0BAA0B;IACvCC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAEd,UAAU,CAACe,cAAc,CAACT,QAAQ,CAAC;IAC1CU,WAAW,EAAE,sBAAsB;IACnCC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAEd,UAAU,CAACe,cAAc,CAACR,SAAS,CAAC;IAC3CS,WAAW,EAAE,kCAAkC;IAC/CC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAEd,UAAU,CAACe,cAAc,CAACP,SAAS,CAAC;IAC3CQ,WAAW,EAAE,wBAAwB;IACrCC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAEd,UAAU,CAACe,cAAc,CAACN,WAAW,CAAC;IAC7CO,WAAW,EAAE,yBAAyB;IACtCC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAEd,UAAU,CAACe,cAAc,CAACL,kBAAkB,CAAC;IACpDM,WAAW,EAAE,uBAAuB;IACpCC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE;EACb,CAAC,CACF;EAED,oBACEhB,OAAA;IAAKiB,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBlB,OAAA;MAAImB,KAAK,EAAE;QAAEC,YAAY,EAAE,MAAM;QAAEL,KAAK,EAAE,MAAM;QAAEM,QAAQ,EAAE;MAAO,CAAE;MAAAH,QAAA,EAAC;IAEtE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLzB,OAAA;MAAKmB,KAAK,EAAE;QACVO,OAAO,EAAE,MAAM;QACfC,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXR,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,EACCR,WAAW,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3B/B,OAAA;QAEEmB,KAAK,EAAE;UACLa,OAAO,EAAE,MAAM;UACfC,eAAe,EAAEH,IAAI,CAACd,SAAS,GAAG,SAAS,GAAG,SAAS;UACvDkB,MAAM,EAAEJ,IAAI,CAACd,SAAS,GAAG,mBAAmB,GAAG,mBAAmB;UAClEmB,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,aAAaN,IAAI,CAACf,KAAK;QACrC,CAAE;QAAAG,QAAA,gBAEFlB,OAAA;UAAKmB,KAAK,EAAE;YACVE,QAAQ,EAAE,MAAM;YAChBN,KAAK,EAAE,SAAS;YAChBK,YAAY,EAAE,KAAK;YACnBiB,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,EACCY,IAAI,CAACnB;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACNzB,OAAA;UAAKmB,KAAK,EAAE;YACVE,QAAQ,EAAES,IAAI,CAACd,SAAS,GAAG,MAAM,GAAG,MAAM;YAC1CqB,UAAU,EAAE,KAAK;YACjBtB,KAAK,EAAEe,IAAI,CAACf,KAAK;YACjBK,YAAY,EAAE;UAChB,CAAE;UAAAF,QAAA,EACCY,IAAI,CAAClB;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACNzB,OAAA;UAAKmB,KAAK,EAAE;YACVE,QAAQ,EAAE,MAAM;YAChBN,KAAK,EAAE,SAAS;YAChBuB,UAAU,EAAE;UACd,CAAE;UAAApB,QAAA,EACCY,IAAI,CAAChB;QAAW;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA,GA/BDM,KAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA;MAAKmB,KAAK,EAAE;QACVa,OAAO,EAAE,MAAM;QACfC,eAAe,EAAE,SAAS;QAC1BE,YAAY,EAAE,KAAK;QACnBD,MAAM,EAAE;MACV,CAAE;MAAAhB,QAAA,gBACAlB,OAAA;QAAImB,KAAK,EAAE;UACTC,YAAY,EAAE,MAAM;UACpBL,KAAK,EAAE,SAAS;UAChBM,QAAQ,EAAE,MAAM;UAChBgB,UAAU,EAAE;QACd,CAAE;QAAAnB,QAAA,EAAC;MAEH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzB,OAAA;QAAKmB,KAAK,EAAE;UACVO,OAAO,EAAE,MAAM;UACfC,mBAAmB,EAAE,sCAAsC;UAC3DC,GAAG,EAAE,MAAM;UACXP,QAAQ,EAAE;QACZ,CAAE;QAAAH,QAAA,gBACAlB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAMmB,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAU,CAAE;YAAAG,QAAA,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7DzB,OAAA;YAAMmB,KAAK,EAAE;cACXoB,UAAU,EAAE,KAAK;cACjBF,UAAU,EAAE,KAAK;cACjBtB,KAAK,EAAE;YACT,CAAE;YAAAG,QAAA,EACCpB,UAAU,CAAC0C,gBAAgB,CAAC/B,gBAAgB;UAAC;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNzB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAMmB,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAU,CAAE;YAAAG,QAAA,EAAC;UAAkB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5DzB,OAAA;YAAMmB,KAAK,EAAE;cACXoB,UAAU,EAAE,KAAK;cACjBF,UAAU,EAAE,KAAK;cACjBtB,KAAK,EAAE;YACT,CAAE;YAAAG,QAAA,EACCpB,UAAU,CAAC0C,gBAAgB,CAAEjC,WAAW,IAAIC,kBAAkB,GAAGD,WAAW,CAAC,GAAI,GAAG;UAAC;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNzB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAMmB,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAU,CAAE;YAAAG,QAAA,EAAC;UAAqB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/DzB,OAAA;YAAMmB,KAAK,EAAE;cACXoB,UAAU,EAAE,KAAK;cACjBF,UAAU,EAAE,KAAK;cACjBtB,KAAK,EAAE;YACT,CAAE;YAAAG,QAAA,EACCpB,UAAU,CAAC0C,gBAAgB,CAAEhC,kBAAkB,IAAIA,kBAAkB,GAAGD,WAAW,CAAC,GAAI,GAAG;UAAC;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKmB,KAAK,EAAE;QACVsB,SAAS,EAAE,MAAM;QACjBT,OAAO,EAAE,MAAM;QACfC,eAAe,EAAE,SAAS;QAC1BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBd,QAAQ,EAAE,MAAM;QAChBN,KAAK,EAAE,SAAS;QAChBuB,UAAU,EAAE;MACd,CAAE;MAAApB,QAAA,gBACAlB,OAAA;QAAAkB,QAAA,EAAQ;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,wPAG9B;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACiB,EAAA,GArLIzC,UAAU;AAuLhB,eAAeA,UAAU;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
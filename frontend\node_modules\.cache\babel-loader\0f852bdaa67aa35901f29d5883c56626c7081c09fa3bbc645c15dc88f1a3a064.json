{"ast": null, "code": "/**\n * API service for Pakistan Income Tax Calculator\n * Handles all communication with the Flask backend\n */\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n/**\n * Custom error class for API errors\n */\nclass ApiError extends Error {\n  constructor(message, status, data) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n}\n\n/**\n * Generic API request handler\n * @param {string} endpoint - API endpoint\n * @param {object} options - Fetch options\n * @returns {Promise} - API response\n */\nasync function apiRequest(endpoint, options = {}) {\n  const url = `${API_BASE_URL}${endpoint}`;\n  const defaultOptions = {\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  };\n  const config = {\n    ...defaultOptions,\n    ...options\n  };\n  try {\n    const response = await fetch(url, config);\n    const data = await response.json();\n    if (!response.ok) {\n      throw new ApiError(data.error || `HTTP error! status: ${response.status}`, response.status, data);\n    }\n    return data;\n  } catch (error) {\n    if (error instanceof ApiError) {\n      throw error;\n    }\n\n    // Network or other errors\n    throw new ApiError('Network error. Please check your connection and try again.', 0, null);\n  }\n}\n\n/**\n * Tax calculation API service\n */\nexport const taxApi = {\n  /**\n   * Calculate tax for given monthly gross salary\n   * @param {number} monthlyGross - Monthly gross salary in PKR\n   * @returns {Promise<object>} - Tax calculation result\n   */\n  async calculateTax(monthlyGross) {\n    const response = await apiRequest('/tax/calculate', {\n      method: 'POST',\n      body: JSON.stringify({\n        monthly_gross: monthlyGross\n      })\n    });\n    return response.data;\n  },\n  /**\n   * Get calculation history\n   * @param {number} limit - Number of recent calculations to fetch\n   * @returns {Promise<Array>} - Array of calculation history\n   */\n  async getCalculationHistory(limit = 10) {\n    const response = await apiRequest(`/tax/history?limit=${limit}`);\n    return response.data;\n  },\n  /**\n   * Health check\n   * @returns {Promise<object>} - Health status\n   */\n  async healthCheck() {\n    return await apiRequest('/health');\n  }\n};\n\n/**\n * Input validation utilities\n */\nexport const validation = {\n  /**\n   * Validate monthly gross salary input\n   * @param {any} value - Input value to validate\n   * @returns {object} - Validation result\n   */\n  validateMonthlyGross(value) {\n    const errors = [];\n\n    // Check if value exists\n    if (value === null || value === undefined || value === '') {\n      errors.push('Monthly gross salary is required');\n      return {\n        isValid: false,\n        errors\n      };\n    }\n\n    // Convert to number\n    const numValue = Number(value);\n\n    // Check if it's a valid number\n    if (isNaN(numValue)) {\n      errors.push('Monthly gross salary must be a valid number');\n      return {\n        isValid: false,\n        errors\n      };\n    }\n\n    // Check if it's positive\n    if (numValue < 0) {\n      errors.push('Monthly gross salary cannot be negative');\n      return {\n        isValid: false,\n        errors\n      };\n    }\n\n    // Check maximum limit (10 million PKR)\n    if (numValue > 10000000) {\n      errors.push('Monthly gross salary exceeds maximum limit (10,000,000 PKR)');\n      return {\n        isValid: false,\n        errors\n      };\n    }\n    return {\n      isValid: true,\n      errors: []\n    };\n  }\n};\n\n/**\n * Utility functions for formatting\n */\nexport const formatters = {\n  /**\n   * Format currency in PKR\n   * @param {number} amount - Amount to format\n   * @returns {string} - Formatted currency string\n   */\n  formatCurrency(amount) {\n    if (typeof amount !== 'number') {\n      return 'PKR 0';\n    }\n    return new Intl.NumberFormat('en-PK', {\n      style: 'currency',\n      currency: 'PKR',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 2\n    }).format(amount);\n  },\n  /**\n   * Format number with commas\n   * @param {number} number - Number to format\n   * @returns {string} - Formatted number string\n   */\n  formatNumber(number) {\n    if (typeof number !== 'number') {\n      return '0';\n    }\n    return new Intl.NumberFormat('en-PK').format(number);\n  },\n  /**\n   * Format percentage\n   * @param {number} value - Value to format as percentage\n   * @param {number} decimals - Number of decimal places\n   * @returns {string} - Formatted percentage string\n   */\n  formatPercentage(value, decimals = 1) {\n    if (typeof value !== 'number') {\n      return '0%';\n    }\n    return `${value.toFixed(decimals)}%`;\n  }\n};\nexport default taxApi;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ApiError", "Error", "constructor", "message", "status", "data", "name", "apiRequest", "endpoint", "options", "url", "defaultOptions", "headers", "config", "response", "fetch", "json", "ok", "error", "taxApi", "calculateTax", "monthlyGross", "method", "body", "JSON", "stringify", "monthly_gross", "getCalculationHistory", "limit", "healthCheck", "validation", "validateMonthlyGross", "value", "errors", "undefined", "push", "<PERSON><PERSON><PERSON><PERSON>", "numValue", "Number", "isNaN", "formatters", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatNumber", "number", "formatPercentage", "decimals", "toFixed"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/services/taxApi.js"], "sourcesContent": ["/**\n * API service for Pakistan Income Tax Calculator\n * Handles all communication with the Flask backend\n */\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n\n/**\n * Custom error class for API errors\n */\nclass ApiError extends Error {\n  constructor(message, status, data) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.data = data;\n  }\n}\n\n/**\n * Generic API request handler\n * @param {string} endpoint - API endpoint\n * @param {object} options - Fetch options\n * @returns {Promise} - API response\n */\nasync function apiRequest(endpoint, options = {}) {\n  const url = `${API_BASE_URL}${endpoint}`;\n  \n  const defaultOptions = {\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  };\n  \n  const config = { ...defaultOptions, ...options };\n  \n  try {\n    const response = await fetch(url, config);\n    const data = await response.json();\n    \n    if (!response.ok) {\n      throw new ApiError(\n        data.error || `HTTP error! status: ${response.status}`,\n        response.status,\n        data\n      );\n    }\n    \n    return data;\n  } catch (error) {\n    if (error instanceof ApiError) {\n      throw error;\n    }\n    \n    // Network or other errors\n    throw new ApiError(\n      'Network error. Please check your connection and try again.',\n      0,\n      null\n    );\n  }\n}\n\n/**\n * Tax calculation API service\n */\nexport const taxApi = {\n  /**\n   * Calculate tax for given monthly gross salary\n   * @param {number} monthlyGross - Monthly gross salary in PKR\n   * @returns {Promise<object>} - Tax calculation result\n   */\n  async calculateTax(monthlyGross) {\n    const response = await apiRequest('/tax/calculate', {\n      method: 'POST',\n      body: JSON.stringify({ monthly_gross: monthlyGross }),\n    });\n    \n    return response.data;\n  },\n  \n  /**\n   * Get calculation history\n   * @param {number} limit - Number of recent calculations to fetch\n   * @returns {Promise<Array>} - Array of calculation history\n   */\n  async getCalculationHistory(limit = 10) {\n    const response = await apiRequest(`/tax/history?limit=${limit}`);\n    return response.data;\n  },\n  \n  /**\n   * Health check\n   * @returns {Promise<object>} - Health status\n   */\n  async healthCheck() {\n    return await apiRequest('/health');\n  }\n};\n\n/**\n * Input validation utilities\n */\nexport const validation = {\n  /**\n   * Validate monthly gross salary input\n   * @param {any} value - Input value to validate\n   * @returns {object} - Validation result\n   */\n  validateMonthlyGross(value) {\n    const errors = [];\n    \n    // Check if value exists\n    if (value === null || value === undefined || value === '') {\n      errors.push('Monthly gross salary is required');\n      return { isValid: false, errors };\n    }\n    \n    // Convert to number\n    const numValue = Number(value);\n    \n    // Check if it's a valid number\n    if (isNaN(numValue)) {\n      errors.push('Monthly gross salary must be a valid number');\n      return { isValid: false, errors };\n    }\n    \n    // Check if it's positive\n    if (numValue < 0) {\n      errors.push('Monthly gross salary cannot be negative');\n      return { isValid: false, errors };\n    }\n    \n    // Check maximum limit (10 million PKR)\n    if (numValue > 10000000) {\n      errors.push('Monthly gross salary exceeds maximum limit (10,000,000 PKR)');\n      return { isValid: false, errors };\n    }\n    \n    return { isValid: true, errors: [] };\n  }\n};\n\n/**\n * Utility functions for formatting\n */\nexport const formatters = {\n  /**\n   * Format currency in PKR\n   * @param {number} amount - Amount to format\n   * @returns {string} - Formatted currency string\n   */\n  formatCurrency(amount) {\n    if (typeof amount !== 'number') {\n      return 'PKR 0';\n    }\n    \n    return new Intl.NumberFormat('en-PK', {\n      style: 'currency',\n      currency: 'PKR',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 2,\n    }).format(amount);\n  },\n  \n  /**\n   * Format number with commas\n   * @param {number} number - Number to format\n   * @returns {string} - Formatted number string\n   */\n  formatNumber(number) {\n    if (typeof number !== 'number') {\n      return '0';\n    }\n    \n    return new Intl.NumberFormat('en-PK').format(number);\n  },\n  \n  /**\n   * Format percentage\n   * @param {number} value - Value to format as percentage\n   * @param {number} decimals - Number of decimal places\n   * @returns {string} - Formatted percentage string\n   */\n  formatPercentage(value, decimals = 1) {\n    if (typeof value !== 'number') {\n      return '0%';\n    }\n    \n    return `${value.toFixed(decimals)}%`;\n  }\n};\n\nexport default taxApi;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA;AACA;AACA,MAAMC,QAAQ,SAASC,KAAK,CAAC;EAC3BC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAE;IACjC,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACG,IAAI,GAAG,UAAU;IACtB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeE,UAAUA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAChD,MAAMC,GAAG,GAAG,GAAGd,YAAY,GAAGY,QAAQ,EAAE;EAExC,MAAMG,cAAc,GAAG;IACrBC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC;EAED,MAAMC,MAAM,GAAG;IAAE,GAAGF,cAAc;IAAE,GAAGF;EAAQ,CAAC;EAEhD,IAAI;IACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACL,GAAG,EAAEG,MAAM,CAAC;IACzC,MAAMR,IAAI,GAAG,MAAMS,QAAQ,CAACE,IAAI,CAAC,CAAC;IAElC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;MAChB,MAAM,IAAIjB,QAAQ,CAChBK,IAAI,CAACa,KAAK,IAAI,uBAAuBJ,QAAQ,CAACV,MAAM,EAAE,EACtDU,QAAQ,CAACV,MAAM,EACfC,IACF,CAAC;IACH;IAEA,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOa,KAAK,EAAE;IACd,IAAIA,KAAK,YAAYlB,QAAQ,EAAE;MAC7B,MAAMkB,KAAK;IACb;;IAEA;IACA,MAAM,IAAIlB,QAAQ,CAChB,4DAA4D,EAC5D,CAAC,EACD,IACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMmB,MAAM,GAAG;EACpB;AACF;AACA;AACA;AACA;EACE,MAAMC,YAAYA,CAACC,YAAY,EAAE;IAC/B,MAAMP,QAAQ,GAAG,MAAMP,UAAU,CAAC,gBAAgB,EAAE;MAClDe,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEC,aAAa,EAAEL;MAAa,CAAC;IACtD,CAAC,CAAC;IAEF,OAAOP,QAAQ,CAACT,IAAI;EACtB,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMsB,qBAAqBA,CAACC,KAAK,GAAG,EAAE,EAAE;IACtC,MAAMd,QAAQ,GAAG,MAAMP,UAAU,CAAC,sBAAsBqB,KAAK,EAAE,CAAC;IAChE,OAAOd,QAAQ,CAACT,IAAI;EACtB,CAAC;EAED;AACF;AACA;AACA;EACE,MAAMwB,WAAWA,CAAA,EAAG;IAClB,OAAO,MAAMtB,UAAU,CAAC,SAAS,CAAC;EACpC;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMuB,UAAU,GAAG;EACxB;AACF;AACA;AACA;AACA;EACEC,oBAAoBA,CAACC,KAAK,EAAE;IAC1B,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,EAAE,EAAE;MACzDC,MAAM,CAACE,IAAI,CAAC,kCAAkC,CAAC;MAC/C,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEH;MAAO,CAAC;IACnC;;IAEA;IACA,MAAMI,QAAQ,GAAGC,MAAM,CAACN,KAAK,CAAC;;IAE9B;IACA,IAAIO,KAAK,CAACF,QAAQ,CAAC,EAAE;MACnBJ,MAAM,CAACE,IAAI,CAAC,6CAA6C,CAAC;MAC1D,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEH;MAAO,CAAC;IACnC;;IAEA;IACA,IAAII,QAAQ,GAAG,CAAC,EAAE;MAChBJ,MAAM,CAACE,IAAI,CAAC,yCAAyC,CAAC;MACtD,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEH;MAAO,CAAC;IACnC;;IAEA;IACA,IAAII,QAAQ,GAAG,QAAQ,EAAE;MACvBJ,MAAM,CAACE,IAAI,CAAC,6DAA6D,CAAC;MAC1E,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEH;MAAO,CAAC;IACnC;IAEA,OAAO;MAAEG,OAAO,EAAE,IAAI;MAAEH,MAAM,EAAE;IAAG,CAAC;EACtC;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,UAAU,GAAG;EACxB;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAACC,MAAM,EAAE;IACrB,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAO,OAAO;IAChB;IAEA,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;EACnB,CAAC;EAED;AACF;AACA;AACA;AACA;EACEQ,YAAYA,CAACC,MAAM,EAAE;IACnB,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAO,GAAG;IACZ;IAEA,OAAO,IAAIR,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACK,MAAM,CAACE,MAAM,CAAC;EACtD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,gBAAgBA,CAACpB,KAAK,EAAEqB,QAAQ,GAAG,CAAC,EAAE;IACpC,IAAI,OAAOrB,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAI;IACb;IAEA,OAAO,GAAGA,KAAK,CAACsB,OAAO,CAACD,QAAQ,CAAC,GAAG;EACtC;AACF,CAAC;AAED,eAAelC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
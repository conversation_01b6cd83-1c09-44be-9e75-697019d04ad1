{"ast": null, "code": "/**\n * Main App Component\n * Pakistan Income Tax Calculator Application\n */import React,{useState,useEffect}from'react';import Header from'./components/Header';import Footer from'./components/Footer';import TaxCalculatorForm from'./components/TaxCalculatorForm';import TaxResults from'./components/TaxResults';import{taxApi}from'./services/taxApi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[taxResults,setTaxResults]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[apiStatus,setApiStatus]=useState('checking');// Check API health on component mount\nuseEffect(()=>{checkApiHealth();},[]);const checkApiHealth=async()=>{try{await taxApi.healthCheck();setApiStatus('healthy');}catch(err){setApiStatus('error');console.error('API health check failed:',err);}};const handleCalculateTax=async monthlyGross=>{setLoading(true);setError(null);try{const results=await taxApi.calculateTax(monthlyGross);setTaxResults(results);}catch(err){setError(err.message||'Failed to calculate tax. Please try again.');setTaxResults(null);}finally{setLoading(false);}};const handleRetry=()=>{setError(null);setTaxResults(null);checkApiHealth();};return/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsxs(\"main\",{className:\"container\",children:[apiStatus==='error'&&/*#__PURE__*/_jsx(\"div\",{className:\"card\",style:{marginBottom:'20px'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-error\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Connection Error:\"}),\" Unable to connect to the backend server. Please ensure the Flask server is running on port 5000.\",/*#__PURE__*/_jsx(\"button\",{onClick:handleRetry,style:{marginLeft:'12px',padding:'4px 12px',background:'transparent',border:'1px solid #c53030',borderRadius:'4px',color:'#c53030',cursor:'pointer',fontSize:'14px'},children:\"Retry\"})]})}),apiStatus==='checking'&&/*#__PURE__*/_jsx(\"div\",{className:\"card\",style:{marginBottom:'20px'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',color:'#666',fontSize:'14px'},children:[/*#__PURE__*/_jsx(\"span\",{className:\"loading\"}),\"Connecting to backend server...\"]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(400px, 1fr))',gap:'30px',alignItems:'start'},children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TaxCalculatorForm,{onCalculate:handleCalculateTax,loading:loading,error:error})}),/*#__PURE__*/_jsxs(\"div\",{children:[taxResults&&/*#__PURE__*/_jsx(TaxResults,{results:taxResults}),!taxResults&&!loading&&!error&&apiStatus==='healthy'&&/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"h2\",{style:{marginBottom:'16px',color:'#333',fontSize:'24px'},children:\"Welcome to Pakistan Tax Calculator\"}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#666',lineHeight:'1.6',marginBottom:'20px',fontSize:'16px'},children:\"Calculate your monthly income tax according to Pakistan's tax rules for Financial Year 2025-26. Simply enter your monthly gross salary to get a detailed breakdown of your tax liability.\"}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'20px',backgroundColor:'#f8fafc',borderRadius:'8px',border:'1px solid #e2e8f0'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'12px',color:'#334155',fontSize:'18px',fontWeight:'600'},children:\"What's Included:\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{margin:0,paddingLeft:'20px',color:'#64748b',lineHeight:'1.6'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"Complete tax calculation based on current slabs\"}),/*#__PURE__*/_jsx(\"li\",{children:\"9% surcharge calculation\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Monthly and annual tax breakdown\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Net take-home salary calculation\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Effective tax rate analysis\"})]})]})]})]})]}),!taxResults&&apiStatus==='healthy'&&/*#__PURE__*/_jsxs(\"div\",{className:\"card\",style:{marginTop:'30px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{marginBottom:'20px',color:'#333',fontSize:'20px'},children:\"Quick Examples\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(200px, 1fr))',gap:'16px'},children:[{salary:50000,description:'Entry Level'},{salary:100000,description:'Mid Level'},{salary:200000,description:'Senior Level'},{salary:350000,description:'Executive Level'}].map((example,index)=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>handleCalculateTax(example.salary),disabled:loading,style:{padding:'16px',background:'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',border:'1px solid #cbd5e1',borderRadius:'8px',cursor:'pointer',transition:'all 0.3s ease',textAlign:'center'},onMouseOver:e=>{e.target.style.transform='translateY(-2px)';e.target.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.1)';},onMouseOut:e=>{e.target.style.transform='translateY(0)';e.target.style.boxShadow='none';},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'600',color:'#334155',marginBottom:'4px'},children:[\"PKR \",example.salary.toLocaleString()]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#64748b'},children:example.description})]},index))})]})]}),/*#__PURE__*/_jsx(Footer,{})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "Footer", "TaxCalculatorForm", "TaxResults", "taxApi", "jsx", "_jsx", "jsxs", "_jsxs", "App", "taxResults", "setTaxResults", "loading", "setLoading", "error", "setError", "api<PERSON><PERSON>us", "setApiStatus", "checkApiHealth", "healthCheck", "err", "console", "handleCalculateTax", "monthlyGross", "results", "calculateTax", "message", "handleRetry", "className", "children", "style", "marginBottom", "onClick", "marginLeft", "padding", "background", "border", "borderRadius", "color", "cursor", "fontSize", "display", "alignItems", "gridTemplateColumns", "gap", "onCalculate", "lineHeight", "backgroundColor", "fontWeight", "margin", "paddingLeft", "marginTop", "salary", "description", "map", "example", "index", "disabled", "transition", "textAlign", "onMouseOver", "e", "target", "transform", "boxShadow", "onMouseOut", "toLocaleString"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/App.js"], "sourcesContent": ["/**\n * Main App Component\n * Pakistan Income Tax Calculator Application\n */\nimport React, { useState, useEffect } from 'react';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport TaxCalculatorForm from './components/TaxCalculatorForm';\nimport TaxResults from './components/TaxResults';\nimport { taxApi } from './services/taxApi';\n\nfunction App() {\n  const [taxResults, setTaxResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [apiStatus, setApiStatus] = useState('checking');\n\n  // Check API health on component mount\n  useEffect(() => {\n    checkApiHealth();\n  }, []);\n\n  const checkApiHealth = async () => {\n    try {\n      await taxApi.healthCheck();\n      setApiStatus('healthy');\n    } catch (err) {\n      setApiStatus('error');\n      console.error('API health check failed:', err);\n    }\n  };\n\n  const handleCalculateTax = async (monthlyGross) => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const results = await taxApi.calculateTax(monthlyGross);\n      setTaxResults(results);\n    } catch (err) {\n      setError(err.message || 'Failed to calculate tax. Please try again.');\n      setTaxResults(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRetry = () => {\n    setError(null);\n    setTaxResults(null);\n    checkApiHealth();\n  };\n\n  return (\n    <div className=\"App\">\n      <Header />\n      \n      <main className=\"container\">\n        {/* API Status Warning */}\n        {apiStatus === 'error' && (\n          <div className=\"card\" style={{ marginBottom: '20px' }}>\n            <div className=\"alert alert-error\">\n              <strong>Connection Error:</strong> Unable to connect to the backend server. \n              Please ensure the Flask server is running on port 5000.\n              <button \n                onClick={handleRetry}\n                style={{\n                  marginLeft: '12px',\n                  padding: '4px 12px',\n                  background: 'transparent',\n                  border: '1px solid #c53030',\n                  borderRadius: '4px',\n                  color: '#c53030',\n                  cursor: 'pointer',\n                  fontSize: '14px'\n                }}\n              >\n                Retry\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* API Status Loading */}\n        {apiStatus === 'checking' && (\n          <div className=\"card\" style={{ marginBottom: '20px' }}>\n            <div style={{ \n              display: 'flex', \n              alignItems: 'center', \n              color: '#666',\n              fontSize: '14px'\n            }}>\n              <span className=\"loading\"></span>\n              Connecting to backend server...\n            </div>\n          </div>\n        )}\n\n        {/* Main Content */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',\n          gap: '30px',\n          alignItems: 'start'\n        }}>\n          {/* Tax Calculator Form */}\n          <div>\n            <TaxCalculatorForm\n              onCalculate={handleCalculateTax}\n              loading={loading}\n              error={error}\n            />\n          </div>\n\n          {/* Tax Results */}\n          <div>\n            {taxResults && <TaxResults results={taxResults} />}\n            \n            {/* Welcome Message when no results */}\n            {!taxResults && !loading && !error && apiStatus === 'healthy' && (\n              <div className=\"card\">\n                <h2 style={{ marginBottom: '16px', color: '#333', fontSize: '24px' }}>\n                  Welcome to Pakistan Tax Calculator\n                </h2>\n                <p style={{ \n                  color: '#666', \n                  lineHeight: '1.6', \n                  marginBottom: '20px',\n                  fontSize: '16px'\n                }}>\n                  Calculate your monthly income tax according to Pakistan's tax rules for \n                  Financial Year 2025-26. Simply enter your monthly gross salary to get \n                  a detailed breakdown of your tax liability.\n                </p>\n                \n                <div style={{\n                  padding: '20px',\n                  backgroundColor: '#f8fafc',\n                  borderRadius: '8px',\n                  border: '1px solid #e2e8f0'\n                }}>\n                  <h3 style={{ \n                    marginBottom: '12px', \n                    color: '#334155', \n                    fontSize: '18px',\n                    fontWeight: '600'\n                  }}>\n                    What's Included:\n                  </h3>\n                  <ul style={{ \n                    margin: 0, \n                    paddingLeft: '20px',\n                    color: '#64748b',\n                    lineHeight: '1.6'\n                  }}>\n                    <li>Complete tax calculation based on current slabs</li>\n                    <li>9% surcharge calculation</li>\n                    <li>Monthly and annual tax breakdown</li>\n                    <li>Net take-home salary calculation</li>\n                    <li>Effective tax rate analysis</li>\n                  </ul>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Examples */}\n        {!taxResults && apiStatus === 'healthy' && (\n          <div className=\"card\" style={{ marginTop: '30px' }}>\n            <h3 style={{ marginBottom: '20px', color: '#333', fontSize: '20px' }}>\n              Quick Examples\n            </h3>\n            <div style={{\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '16px'\n            }}>\n              {[\n                { salary: 50000, description: 'Entry Level' },\n                { salary: 100000, description: 'Mid Level' },\n                { salary: 200000, description: 'Senior Level' },\n                { salary: 350000, description: 'Executive Level' }\n              ].map((example, index) => (\n                <button\n                  key={index}\n                  onClick={() => handleCalculateTax(example.salary)}\n                  disabled={loading}\n                  style={{\n                    padding: '16px',\n                    background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',\n                    border: '1px solid #cbd5e1',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    textAlign: 'center'\n                  }}\n                  onMouseOver={(e) => {\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = 'none';\n                  }}\n                >\n                  <div style={{ fontWeight: '600', color: '#334155', marginBottom: '4px' }}>\n                    PKR {example.salary.toLocaleString()}\n                  </div>\n                  <div style={{ fontSize: '14px', color: '#64748b' }}>\n                    {example.description}\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,OAASC,MAAM,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACc,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgB,KAAK,CAAEC,QAAQ,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAC,UAAU,CAAC,CAEtD;AACAC,SAAS,CAAC,IAAM,CACdmB,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF,KAAM,CAAAd,MAAM,CAACe,WAAW,CAAC,CAAC,CAC1BF,YAAY,CAAC,SAAS,CAAC,CACzB,CAAE,MAAOG,GAAG,CAAE,CACZH,YAAY,CAAC,OAAO,CAAC,CACrBI,OAAO,CAACP,KAAK,CAAC,0BAA0B,CAAEM,GAAG,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAG,KAAO,CAAAC,YAAY,EAAK,CACjDV,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAS,OAAO,CAAG,KAAM,CAAApB,MAAM,CAACqB,YAAY,CAACF,YAAY,CAAC,CACvDZ,aAAa,CAACa,OAAO,CAAC,CACxB,CAAE,MAAOJ,GAAG,CAAE,CACZL,QAAQ,CAACK,GAAG,CAACM,OAAO,EAAI,4CAA4C,CAAC,CACrEf,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAc,WAAW,CAAGA,CAAA,GAAM,CACxBZ,QAAQ,CAAC,IAAI,CAAC,CACdJ,aAAa,CAAC,IAAI,CAAC,CACnBO,cAAc,CAAC,CAAC,CAClB,CAAC,CAED,mBACEV,KAAA,QAAKoB,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBvB,IAAA,CAACN,MAAM,GAAE,CAAC,cAEVQ,KAAA,SAAMoB,SAAS,CAAC,WAAW,CAAAC,QAAA,EAExBb,SAAS,GAAK,OAAO,eACpBV,IAAA,QAAKsB,SAAS,CAAC,MAAM,CAACE,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAF,QAAA,cACpDrB,KAAA,QAAKoB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCvB,IAAA,WAAAuB,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,oGAElC,cAAAvB,IAAA,WACE0B,OAAO,CAAEL,WAAY,CACrBG,KAAK,CAAE,CACLG,UAAU,CAAE,MAAM,CAClBC,OAAO,CAAE,UAAU,CACnBC,UAAU,CAAE,aAAa,CACzBC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MACZ,CAAE,CAAAX,QAAA,CACH,OAED,CAAQ,CAAC,EACN,CAAC,CACH,CACN,CAGAb,SAAS,GAAK,UAAU,eACvBV,IAAA,QAAKsB,SAAS,CAAC,MAAM,CAACE,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAF,QAAA,cACpDrB,KAAA,QAAKsB,KAAK,CAAE,CACVW,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBJ,KAAK,CAAE,MAAM,CACbE,QAAQ,CAAE,MACZ,CAAE,CAAAX,QAAA,eACAvB,IAAA,SAAMsB,SAAS,CAAC,SAAS,CAAO,CAAC,kCAEnC,EAAK,CAAC,CACH,CACN,cAGDpB,KAAA,QAAKsB,KAAK,CAAE,CACVW,OAAO,CAAE,MAAM,CACfE,mBAAmB,CAAE,sCAAsC,CAC3DC,GAAG,CAAE,MAAM,CACXF,UAAU,CAAE,OACd,CAAE,CAAAb,QAAA,eAEAvB,IAAA,QAAAuB,QAAA,cACEvB,IAAA,CAACJ,iBAAiB,EAChB2C,WAAW,CAAEvB,kBAAmB,CAChCV,OAAO,CAAEA,OAAQ,CACjBE,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,cAGNN,KAAA,QAAAqB,QAAA,EACGnB,UAAU,eAAIJ,IAAA,CAACH,UAAU,EAACqB,OAAO,CAAEd,UAAW,CAAE,CAAC,CAGjD,CAACA,UAAU,EAAI,CAACE,OAAO,EAAI,CAACE,KAAK,EAAIE,SAAS,GAAK,SAAS,eAC3DR,KAAA,QAAKoB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvB,IAAA,OAAIwB,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEO,KAAK,CAAE,MAAM,CAAEE,QAAQ,CAAE,MAAO,CAAE,CAAAX,QAAA,CAAC,oCAEtE,CAAI,CAAC,cACLvB,IAAA,MAAGwB,KAAK,CAAE,CACRQ,KAAK,CAAE,MAAM,CACbQ,UAAU,CAAE,KAAK,CACjBf,YAAY,CAAE,MAAM,CACpBS,QAAQ,CAAE,MACZ,CAAE,CAAAX,QAAA,CAAC,2LAIH,CAAG,CAAC,cAEJrB,KAAA,QAAKsB,KAAK,CAAE,CACVI,OAAO,CAAE,MAAM,CACfa,eAAe,CAAE,SAAS,CAC1BV,YAAY,CAAE,KAAK,CACnBD,MAAM,CAAE,mBACV,CAAE,CAAAP,QAAA,eACAvB,IAAA,OAAIwB,KAAK,CAAE,CACTC,YAAY,CAAE,MAAM,CACpBO,KAAK,CAAE,SAAS,CAChBE,QAAQ,CAAE,MAAM,CAChBQ,UAAU,CAAE,KACd,CAAE,CAAAnB,QAAA,CAAC,kBAEH,CAAI,CAAC,cACLrB,KAAA,OAAIsB,KAAK,CAAE,CACTmB,MAAM,CAAE,CAAC,CACTC,WAAW,CAAE,MAAM,CACnBZ,KAAK,CAAE,SAAS,CAChBQ,UAAU,CAAE,KACd,CAAE,CAAAjB,QAAA,eACAvB,IAAA,OAAAuB,QAAA,CAAI,iDAA+C,CAAI,CAAC,cACxDvB,IAAA,OAAAuB,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCvB,IAAA,OAAAuB,QAAA,CAAI,kCAAgC,CAAI,CAAC,cACzCvB,IAAA,OAAAuB,QAAA,CAAI,kCAAgC,CAAI,CAAC,cACzCvB,IAAA,OAAAuB,QAAA,CAAI,6BAA2B,CAAI,CAAC,EAClC,CAAC,EACF,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,CAGL,CAACnB,UAAU,EAAIM,SAAS,GAAK,SAAS,eACrCR,KAAA,QAAKoB,SAAS,CAAC,MAAM,CAACE,KAAK,CAAE,CAAEqB,SAAS,CAAE,MAAO,CAAE,CAAAtB,QAAA,eACjDvB,IAAA,OAAIwB,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAM,CAAEO,KAAK,CAAE,MAAM,CAAEE,QAAQ,CAAE,MAAO,CAAE,CAAAX,QAAA,CAAC,gBAEtE,CAAI,CAAC,cACLvB,IAAA,QAAKwB,KAAK,CAAE,CACVW,OAAO,CAAE,MAAM,CACfE,mBAAmB,CAAE,sCAAsC,CAC3DC,GAAG,CAAE,MACP,CAAE,CAAAf,QAAA,CACC,CACC,CAAEuB,MAAM,CAAE,KAAK,CAAEC,WAAW,CAAE,aAAc,CAAC,CAC7C,CAAED,MAAM,CAAE,MAAM,CAAEC,WAAW,CAAE,WAAY,CAAC,CAC5C,CAAED,MAAM,CAAE,MAAM,CAAEC,WAAW,CAAE,cAAe,CAAC,CAC/C,CAAED,MAAM,CAAE,MAAM,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CACnD,CAACC,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBACnBhD,KAAA,WAEEwB,OAAO,CAAEA,CAAA,GAAMV,kBAAkB,CAACiC,OAAO,CAACH,MAAM,CAAE,CAClDK,QAAQ,CAAE7C,OAAQ,CAClBkB,KAAK,CAAE,CACLI,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,mDAAmD,CAC/DC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBE,MAAM,CAAE,SAAS,CACjBmB,UAAU,CAAE,eAAe,CAC3BC,SAAS,CAAE,QACb,CAAE,CACFC,WAAW,CAAGC,CAAC,EAAK,CAClBA,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACiC,SAAS,CAAG,kBAAkB,CAC7CF,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACkC,SAAS,CAAG,+BAA+B,CAC5D,CAAE,CACFC,UAAU,CAAGJ,CAAC,EAAK,CACjBA,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACiC,SAAS,CAAG,eAAe,CAC1CF,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACkC,SAAS,CAAG,MAAM,CACnC,CAAE,CAAAnC,QAAA,eAEFrB,KAAA,QAAKsB,KAAK,CAAE,CAAEkB,UAAU,CAAE,KAAK,CAAEV,KAAK,CAAE,SAAS,CAAEP,YAAY,CAAE,KAAM,CAAE,CAAAF,QAAA,EAAC,MACpE,CAAC0B,OAAO,CAACH,MAAM,CAACc,cAAc,CAAC,CAAC,EACjC,CAAC,cACN5D,IAAA,QAAKwB,KAAK,CAAE,CAAEU,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,SAAU,CAAE,CAAAT,QAAA,CAChD0B,OAAO,CAACF,WAAW,CACjB,CAAC,GA1BDG,KA2BC,CACT,CAAC,CACC,CAAC,EACH,CACN,EACG,CAAC,cAEPlD,IAAA,CAACL,MAAM,GAAE,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAAQ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
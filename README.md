# Pakistan Income Tax Calculator (FY 2025–26)

A full-stack web application to calculate monthly income tax in Pakistan according to the 2025–2026 tax rules.

## Tech Stack

- **Frontend**: React (responsive, mobile-friendly)
- **Backend**: Python Flask (REST API)
- **Database**: SQLite (calculation logging)

## Project Structure

```
my-tax-calculator/
├── backend/                 # Flask API server
│   ├── app/
│   │   ├── __init__.py
│   │   ├── models.py       # Database models
│   │   ├── routes.py       # API endpoints
│   │   └── tax_calculator.py # Tax calculation logic
│   ├── tests/              # Unit tests
│   ├── requirements.txt    # Python dependencies
│   └── run.py             # Flask app entry point
├── frontend/               # React application
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── services/      # API service layer
│   │   └── App.js        # Main app component
│   ├── public/
│   └── package.json      # Node dependencies
└── README.md
```

## Tax Rules (FY 2025-26)

- **Currency**: PKR
- **Surcharge**: 9% of base tax (only when annual income exceeds Rs. 10 million)
- **Tax Slabs**:
  - 0 – 600,000: 0%
  - 600,001 – 1,200,000: 1% of (income - 600,000)
  - 1,200,001 – 2,200,000: 6,000 + 11% of (income - 1,200,000)
  - 2,200,001 – 3,200,000: 116,000 + 23% of (income - 2,200,000)
  - 3,200,001 – 4,100,000: 346,000 + 30% of (income - 3,200,000)
  - Above 4,100,000: 616,000 + 35% of (income - 4,100,000)

## Quick Start

### Prerequisites
- Python 3.8+ installed
- Node.js 16+ and npm installed (for frontend)

### Backend Setup (Flask API)

1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Create and activate virtual environment:
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # Linux/Mac
   python3 -m venv venv
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run tests to verify setup:
   ```bash
   python -m pytest tests/ -v
   ```

5. Start the Flask server:
   ```bash
   python run.py
   ```

   The API will be available at: `http://localhost:5000`

### Frontend Setup (React)

1. Navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start development server:
   ```bash
   npm start
   ```

   The frontend will be available at: `http://localhost:3000`

### Quick Test

Once both servers are running, you can test the API directly:

```bash
# Test health endpoint
curl http://localhost:5000/api/health

# Test tax calculation
curl -X POST http://localhost:5000/api/tax/calculate \
  -H "Content-Type: application/json" \
  -d '{"monthly_gross": 100000}'
```

## API Documentation

The backend provides Swagger/OpenAPI documentation at: `http://localhost:5000/api/docs`

### Main Endpoint

**POST** `/api/tax/calculate`

**Request Body**:
```json
{
  "monthly_gross": 100000
}
```

**Response**:
```json
{
  "annual_income": 1200000,
  "base_tax": 6000,
  "surcharge": 0,
  "total_tax": 6000,
  "monthly_tax": 500,
  "net_monthly_salary": 99500
}
```

## Development

- Backend runs on `http://localhost:5000`
- Frontend runs on `http://localhost:3000`
- Database: SQLite file created automatically

## Testing

Run backend tests:
```bash
cd backend
python -m pytest tests/
```

## Features

- ✅ Accurate tax calculation based on FY 2025-26 rules
- ✅ Responsive mobile-friendly design
- ✅ API-driven architecture
- ✅ Calculation history logging
- ✅ Input validation and error handling
- ✅ Swagger API documentation

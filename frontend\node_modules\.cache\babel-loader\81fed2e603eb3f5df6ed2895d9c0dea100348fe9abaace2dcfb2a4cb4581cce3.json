{"ast": null, "code": "/**\n * Footer Component\n * Application footer with additional information and links\n */import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Footer=()=>{return/*#__PURE__*/_jsx(\"footer\",{style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',borderTop:'1px solid rgba(255, 255, 255, 0.2)',padding:'30px 0',marginTop:'40px',color:'rgba(255, 255, 255, 0.9)'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(250px, 1fr))',gap:'30px',marginBottom:'20px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{style:{fontSize:'18px',fontWeight:'600',marginBottom:'12px',color:'white'},children:\"About This Calculator\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'14px',lineHeight:'1.6',margin:0,color:'rgba(255, 255, 255, 0.8)'},children:\"This calculator helps you estimate your income tax liability according to Pakistan's tax rules for Financial Year 2025-26. It includes all current tax slabs and the 9% surcharge.\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{style:{fontSize:'18px',fontWeight:'600',marginBottom:'12px',color:'white'},children:\"Features\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{fontSize:'14px',lineHeight:'1.6',margin:0,paddingLeft:'20px',color:'rgba(255, 255, 255, 0.8)'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"Accurate tax calculation\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Mobile-friendly design\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Real-time results\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Detailed breakdown\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{style:{fontSize:'18px',fontWeight:'600',marginBottom:'12px',color:'white'},children:\"Important Notes\"}),/*#__PURE__*/_jsxs(\"ul\",{style:{fontSize:'14px',lineHeight:'1.6',margin:0,paddingLeft:'20px',color:'rgba(255, 255, 255, 0.8)'},children:[/*#__PURE__*/_jsx(\"li\",{children:\"For salaried individuals only\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Excludes pension income\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Based on FY 2025-26 rules\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Consult tax advisor for accuracy\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{borderTop:'1px solid rgba(255, 255, 255, 0.2)',paddingTop:'20px',display:'flex',justifyContent:'space-between',alignItems:'center',flexWrap:'wrap',gap:'16px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'rgba(255, 255, 255, 0.7)'},children:\"\\xA9 2025 Pakistan Income Tax Calculator. Built with React and Flask.\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'20px',fontSize:'14px'},children:[/*#__PURE__*/_jsx(\"a\",{href:\"/api/docs\",target:\"_blank\",rel:\"noopener noreferrer\",style:{color:'rgba(255, 255, 255, 0.8)',textDecoration:'none',transition:'color 0.3s ease'},onMouseOver:e=>e.target.style.color='white',onMouseOut:e=>e.target.style.color='rgba(255, 255, 255, 0.8)',children:\"API Documentation\"}),/*#__PURE__*/_jsx(\"span\",{style:{color:'rgba(255, 255, 255, 0.5)'},children:\"|\"}),/*#__PURE__*/_jsx(\"span\",{style:{color:'rgba(255, 255, 255, 0.8)'},children:\"Version 1.0.0\"})]})]})]})});};export default Footer;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Footer", "style", "background", "<PERSON><PERSON>ilter", "borderTop", "padding", "marginTop", "color", "children", "className", "display", "gridTemplateColumns", "gap", "marginBottom", "fontSize", "fontWeight", "lineHeight", "margin", "paddingLeft", "paddingTop", "justifyContent", "alignItems", "flexWrap", "href", "target", "rel", "textDecoration", "transition", "onMouseOver", "e", "onMouseOut"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/Footer.js"], "sourcesContent": ["/**\n * Footer Component\n * Application footer with additional information and links\n */\nimport React from 'react';\n\nconst Footer = () => {\n  return (\n    <footer style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(10px)',\n      borderTop: '1px solid rgba(255, 255, 255, 0.2)',\n      padding: '30px 0',\n      marginTop: '40px',\n      color: 'rgba(255, 255, 255, 0.9)'\n    }}>\n      <div className=\"container\">\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '30px',\n          marginBottom: '20px'\n        }}>\n          {/* About Section */}\n          <div>\n            <h3 style={{\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            }}>\n              About This Calculator\n            </h3>\n            <p style={{\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)'\n            }}>\n              This calculator helps you estimate your income tax liability according to \n              Pakistan's tax rules for Financial Year 2025-26. It includes all current \n              tax slabs and the 9% surcharge.\n            </p>\n          </div>\n\n          {/* Features Section */}\n          <div>\n            <h3 style={{\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            }}>\n              Features\n            </h3>\n            <ul style={{\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              paddingLeft: '20px',\n              color: 'rgba(255, 255, 255, 0.8)'\n            }}>\n              <li>Accurate tax calculation</li>\n              <li>Mobile-friendly design</li>\n              <li>Real-time results</li>\n              <li>Detailed breakdown</li>\n            </ul>\n          </div>\n\n          {/* Important Notes */}\n          <div>\n            <h3 style={{\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            }}>\n              Important Notes\n            </h3>\n            <ul style={{\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              paddingLeft: '20px',\n              color: 'rgba(255, 255, 255, 0.8)'\n            }}>\n              <li>For salaried individuals only</li>\n              <li>Excludes pension income</li>\n              <li>Based on FY 2025-26 rules</li>\n              <li>Consult tax advisor for accuracy</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div style={{\n          borderTop: '1px solid rgba(255, 255, 255, 0.2)',\n          paddingTop: '20px',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          flexWrap: 'wrap',\n          gap: '16px'\n        }}>\n          <div style={{\n            fontSize: '14px',\n            color: 'rgba(255, 255, 255, 0.7)'\n          }}>\n            © 2025 Pakistan Income Tax Calculator. Built with React and Flask.\n          </div>\n          \n          <div style={{\n            display: 'flex',\n            gap: '20px',\n            fontSize: '14px'\n          }}>\n            <a \n              href=\"/api/docs\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              style={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                transition: 'color 0.3s ease'\n              }}\n              onMouseOver={(e) => e.target.style.color = 'white'}\n              onMouseOut={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.8)'}\n            >\n              API Documentation\n            </a>\n            <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>|</span>\n            <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n              Version 1.0.0\n            </span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": "AAAA;AACA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACEH,IAAA,WAAQI,KAAK,CAAE,CACbC,UAAU,CAAE,0BAA0B,CACtCC,cAAc,CAAE,YAAY,CAC5BC,SAAS,CAAE,oCAAoC,CAC/CC,OAAO,CAAE,QAAQ,CACjBC,SAAS,CAAE,MAAM,CACjBC,KAAK,CAAE,0BACT,CAAE,CAAAC,QAAA,cACAT,KAAA,QAAKU,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBT,KAAA,QAAKE,KAAK,CAAE,CACVS,OAAO,CAAE,MAAM,CACfC,mBAAmB,CAAE,sCAAsC,CAC3DC,GAAG,CAAE,MAAM,CACXC,YAAY,CAAE,MAChB,CAAE,CAAAL,QAAA,eAEAT,KAAA,QAAAS,QAAA,eACEX,IAAA,OAAII,KAAK,CAAE,CACTa,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBF,YAAY,CAAE,MAAM,CACpBN,KAAK,CAAE,OACT,CAAE,CAAAC,QAAA,CAAC,uBAEH,CAAI,CAAC,cACLX,IAAA,MAAGI,KAAK,CAAE,CACRa,QAAQ,CAAE,MAAM,CAChBE,UAAU,CAAE,KAAK,CACjBC,MAAM,CAAE,CAAC,CACTV,KAAK,CAAE,0BACT,CAAE,CAAAC,QAAA,CAAC,oLAIH,CAAG,CAAC,EACD,CAAC,cAGNT,KAAA,QAAAS,QAAA,eACEX,IAAA,OAAII,KAAK,CAAE,CACTa,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBF,YAAY,CAAE,MAAM,CACpBN,KAAK,CAAE,OACT,CAAE,CAAAC,QAAA,CAAC,UAEH,CAAI,CAAC,cACLT,KAAA,OAAIE,KAAK,CAAE,CACTa,QAAQ,CAAE,MAAM,CAChBE,UAAU,CAAE,KAAK,CACjBC,MAAM,CAAE,CAAC,CACTC,WAAW,CAAE,MAAM,CACnBX,KAAK,CAAE,0BACT,CAAE,CAAAC,QAAA,eACAX,IAAA,OAAAW,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCX,IAAA,OAAAW,QAAA,CAAI,wBAAsB,CAAI,CAAC,cAC/BX,IAAA,OAAAW,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BX,IAAA,OAAAW,QAAA,CAAI,oBAAkB,CAAI,CAAC,EACzB,CAAC,EACF,CAAC,cAGNT,KAAA,QAAAS,QAAA,eACEX,IAAA,OAAII,KAAK,CAAE,CACTa,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBF,YAAY,CAAE,MAAM,CACpBN,KAAK,CAAE,OACT,CAAE,CAAAC,QAAA,CAAC,iBAEH,CAAI,CAAC,cACLT,KAAA,OAAIE,KAAK,CAAE,CACTa,QAAQ,CAAE,MAAM,CAChBE,UAAU,CAAE,KAAK,CACjBC,MAAM,CAAE,CAAC,CACTC,WAAW,CAAE,MAAM,CACnBX,KAAK,CAAE,0BACT,CAAE,CAAAC,QAAA,eACAX,IAAA,OAAAW,QAAA,CAAI,+BAA6B,CAAI,CAAC,cACtCX,IAAA,OAAAW,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCX,IAAA,OAAAW,QAAA,CAAI,2BAAyB,CAAI,CAAC,cAClCX,IAAA,OAAAW,QAAA,CAAI,kCAAgC,CAAI,CAAC,EACvC,CAAC,EACF,CAAC,EACH,CAAC,cAGNT,KAAA,QAAKE,KAAK,CAAE,CACVG,SAAS,CAAE,oCAAoC,CAC/Ce,UAAU,CAAE,MAAM,CAClBT,OAAO,CAAE,MAAM,CACfU,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MAAM,CAChBV,GAAG,CAAE,MACP,CAAE,CAAAJ,QAAA,eACAX,IAAA,QAAKI,KAAK,CAAE,CACVa,QAAQ,CAAE,MAAM,CAChBP,KAAK,CAAE,0BACT,CAAE,CAAAC,QAAA,CAAC,uEAEH,CAAK,CAAC,cAENT,KAAA,QAAKE,KAAK,CAAE,CACVS,OAAO,CAAE,MAAM,CACfE,GAAG,CAAE,MAAM,CACXE,QAAQ,CAAE,MACZ,CAAE,CAAAN,QAAA,eACAX,IAAA,MACE0B,IAAI,CAAC,WAAW,CAChBC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBxB,KAAK,CAAE,CACLM,KAAK,CAAE,0BAA0B,CACjCmB,cAAc,CAAE,MAAM,CACtBC,UAAU,CAAE,iBACd,CAAE,CACFC,WAAW,CAAGC,CAAC,EAAKA,CAAC,CAACL,MAAM,CAACvB,KAAK,CAACM,KAAK,CAAG,OAAQ,CACnDuB,UAAU,CAAGD,CAAC,EAAKA,CAAC,CAACL,MAAM,CAACvB,KAAK,CAACM,KAAK,CAAG,0BAA2B,CAAAC,QAAA,CACtE,mBAED,CAAG,CAAC,cACJX,IAAA,SAAMI,KAAK,CAAE,CAAEM,KAAK,CAAE,0BAA2B,CAAE,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,cAC5DX,IAAA,SAAMI,KAAK,CAAE,CAAEM,KAAK,CAAE,0BAA2B,CAAE,CAAAC,QAAA,CAAC,eAEpD,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Hamza\\\\my-tax-calculator\\\\frontend\\\\src\\\\components\\\\Footer.js\";\n/**\n * Footer Component\n * Application footer with additional information and links\n */\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    style: {\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(10px)',\n      borderTop: '1px solid rgba(255, 255, 255, 0.2)',\n      padding: '30px 0',\n      marginTop: '40px',\n      color: 'rgba(255, 255, 255, 0.9)'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '30px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            },\n            children: \"About This Calculator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)'\n            },\n            children: \"This calculator helps you estimate your income tax liability according to Pakistan's tax rules for Financial Year 2025-26. It includes all current tax slabs and the 9% surcharge.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            },\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              paddingLeft: '20px',\n              color: 'rgba(255, 255, 255, 0.8)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Accurate tax calculation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Mobile-friendly design\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Real-time results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Detailed breakdown\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            },\n            children: \"Important Notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              paddingLeft: '20px',\n              color: 'rgba(255, 255, 255, 0.8)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"For salaried individuals only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Excludes pension income\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Based on FY 2025-26 rules\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Consult tax advisor for accuracy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderTop: '1px solid rgba(255, 255, 255, 0.2)',\n          paddingTop: '20px',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          flexWrap: 'wrap',\n          gap: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: 'rgba(255, 255, 255, 0.7)'\n          },\n          children: \"\\xA9 2025 Pakistan Income Tax Calculator. Built with React and Flask.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '20px',\n            fontSize: '14px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/api/docs\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            style: {\n              color: 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              transition: 'color 0.3s ease'\n            },\n            onMouseOver: e => e.target.style.color = 'white',\n            onMouseOut: e => e.target.style.color = 'rgba(255, 255, 255, 0.8)',\n            children: \"API Documentation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'rgba(255, 255, 255, 0.5)'\n            },\n            children: \"|\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'rgba(255, 255, 255, 0.8)'\n            },\n            children: \"Version 1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "style", "background", "<PERSON><PERSON>ilter", "borderTop", "padding", "marginTop", "color", "children", "className", "display", "gridTemplateColumns", "gap", "marginBottom", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lineHeight", "margin", "paddingLeft", "paddingTop", "justifyContent", "alignItems", "flexWrap", "href", "target", "rel", "textDecoration", "transition", "onMouseOver", "e", "onMouseOut", "_c", "$RefreshReg$"], "sources": ["C:/Hamza/my-tax-calculator/frontend/src/components/Footer.js"], "sourcesContent": ["/**\n * Footer Component\n * Application footer with additional information and links\n */\nimport React from 'react';\n\nconst Footer = () => {\n  return (\n    <footer style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(10px)',\n      borderTop: '1px solid rgba(255, 255, 255, 0.2)',\n      padding: '30px 0',\n      marginTop: '40px',\n      color: 'rgba(255, 255, 255, 0.9)'\n    }}>\n      <div className=\"container\">\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '30px',\n          marginBottom: '20px'\n        }}>\n          {/* About Section */}\n          <div>\n            <h3 style={{\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            }}>\n              About This Calculator\n            </h3>\n            <p style={{\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)'\n            }}>\n              This calculator helps you estimate your income tax liability according to \n              Pakistan's tax rules for Financial Year 2025-26. It includes all current \n              tax slabs and the 9% surcharge.\n            </p>\n          </div>\n\n          {/* Features Section */}\n          <div>\n            <h3 style={{\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            }}>\n              Features\n            </h3>\n            <ul style={{\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              paddingLeft: '20px',\n              color: 'rgba(255, 255, 255, 0.8)'\n            }}>\n              <li>Accurate tax calculation</li>\n              <li>Mobile-friendly design</li>\n              <li>Real-time results</li>\n              <li>Detailed breakdown</li>\n            </ul>\n          </div>\n\n          {/* Important Notes */}\n          <div>\n            <h3 style={{\n              fontSize: '18px',\n              fontWeight: '600',\n              marginBottom: '12px',\n              color: 'white'\n            }}>\n              Important Notes\n            </h3>\n            <ul style={{\n              fontSize: '14px',\n              lineHeight: '1.6',\n              margin: 0,\n              paddingLeft: '20px',\n              color: 'rgba(255, 255, 255, 0.8)'\n            }}>\n              <li>For salaried individuals only</li>\n              <li>Excludes pension income</li>\n              <li>Based on FY 2025-26 rules</li>\n              <li>Consult tax advisor for accuracy</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div style={{\n          borderTop: '1px solid rgba(255, 255, 255, 0.2)',\n          paddingTop: '20px',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          flexWrap: 'wrap',\n          gap: '16px'\n        }}>\n          <div style={{\n            fontSize: '14px',\n            color: 'rgba(255, 255, 255, 0.7)'\n          }}>\n            © 2025 Pakistan Income Tax Calculator. Built with React and Flask.\n          </div>\n          \n          <div style={{\n            display: 'flex',\n            gap: '20px',\n            fontSize: '14px'\n          }}>\n            <a \n              href=\"/api/docs\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              style={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                transition: 'color 0.3s ease'\n              }}\n              onMouseOver={(e) => e.target.style.color = 'white'}\n              onMouseOut={(e) => e.target.style.color = 'rgba(255, 255, 255, 0.8)'}\n            >\n              API Documentation\n            </a>\n            <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>|</span>\n            <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>\n              Version 1.0.0\n            </span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,KAAK,EAAE;MACbC,UAAU,EAAE,0BAA0B;MACtCC,cAAc,EAAE,YAAY;MAC5BC,SAAS,EAAE,oCAAoC;MAC/CC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,eACAT,OAAA;MAAKU,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACxBT,OAAA;QAAKE,KAAK,EAAE;UACVS,OAAO,EAAE,MAAM;UACfC,mBAAmB,EAAE,sCAAsC;UAC3DC,GAAG,EAAE,MAAM;UACXC,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBAEAT,OAAA;UAAAS,QAAA,gBACET,OAAA;YAAIE,KAAK,EAAE;cACTa,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBF,YAAY,EAAE,MAAM;cACpBN,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpB,OAAA;YAAGE,KAAK,EAAE;cACRa,QAAQ,EAAE,MAAM;cAChBM,UAAU,EAAE,KAAK;cACjBC,MAAM,EAAE,CAAC;cACTd,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAIH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNpB,OAAA;UAAAS,QAAA,gBACET,OAAA;YAAIE,KAAK,EAAE;cACTa,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBF,YAAY,EAAE,MAAM;cACpBN,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpB,OAAA;YAAIE,KAAK,EAAE;cACTa,QAAQ,EAAE,MAAM;cAChBM,UAAU,EAAE,KAAK;cACjBC,MAAM,EAAE,CAAC;cACTC,WAAW,EAAE,MAAM;cACnBf,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,gBACAT,OAAA;cAAAS,QAAA,EAAI;YAAwB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjCpB,OAAA;cAAAS,QAAA,EAAI;YAAsB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BpB,OAAA;cAAAS,QAAA,EAAI;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BpB,OAAA;cAAAS,QAAA,EAAI;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNpB,OAAA;UAAAS,QAAA,gBACET,OAAA;YAAIE,KAAK,EAAE;cACTa,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBF,YAAY,EAAE,MAAM;cACpBN,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpB,OAAA;YAAIE,KAAK,EAAE;cACTa,QAAQ,EAAE,MAAM;cAChBM,UAAU,EAAE,KAAK;cACjBC,MAAM,EAAE,CAAC;cACTC,WAAW,EAAE,MAAM;cACnBf,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,gBACAT,OAAA;cAAAS,QAAA,EAAI;YAA6B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtCpB,OAAA;cAAAS,QAAA,EAAI;YAAuB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCpB,OAAA;cAAAS,QAAA,EAAI;YAAyB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCpB,OAAA;cAAAS,QAAA,EAAI;YAAgC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpB,OAAA;QAAKE,KAAK,EAAE;UACVG,SAAS,EAAE,oCAAoC;UAC/CmB,UAAU,EAAE,MAAM;UAClBb,OAAO,EAAE,MAAM;UACfc,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,MAAM;UAChBd,GAAG,EAAE;QACP,CAAE;QAAAJ,QAAA,gBACAT,OAAA;UAAKE,KAAK,EAAE;YACVa,QAAQ,EAAE,MAAM;YAChBP,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAENpB,OAAA;UAAKE,KAAK,EAAE;YACVS,OAAO,EAAE,MAAM;YACfE,GAAG,EAAE,MAAM;YACXE,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,gBACAT,OAAA;YACE4B,IAAI,EAAC,WAAW;YAChBC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzB5B,KAAK,EAAE;cACLM,KAAK,EAAE,0BAA0B;cACjCuB,cAAc,EAAE,MAAM;cACtBC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACL,MAAM,CAAC3B,KAAK,CAACM,KAAK,GAAG,OAAQ;YACnD2B,UAAU,EAAGD,CAAC,IAAKA,CAAC,CAACL,MAAM,CAAC3B,KAAK,CAACM,KAAK,GAAG,0BAA2B;YAAAC,QAAA,EACtE;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJpB,OAAA;YAAME,KAAK,EAAE;cAAEM,KAAK,EAAE;YAA2B,CAAE;YAAAC,QAAA,EAAC;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5DpB,OAAA;YAAME,KAAK,EAAE;cAAEM,KAAK,EAAE;YAA2B,CAAE;YAAAC,QAAA,EAAC;UAEpD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACgB,EAAA,GArIInC,MAAM;AAuIZ,eAAeA,MAAM;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"version": 3, "file": "static/css/main.0c3a2313.css", "mappings": "AAEA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,kDAA6D,CAE7D,UAAW,CAPX,mIAEY,CAIZ,gBAEF,CAEA,KACE,uEAEF,CAGA,WAEE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAGA,MACE,eAAiB,CACjB,kBAAmB,CACnB,gCAA0C,CAE1C,kBAAmB,CADnB,YAEF,CAGA,KAEE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAGf,oBAAqB,CALrB,cAAe,CACf,eAAgB,CAJhB,iBAAkB,CASlB,iBAAkB,CAFlB,oBAAqB,CADrB,uBAIF,CAEA,aACE,kDAA6D,CAC7D,UACF,CAEA,mBAEE,+BAA+C,CAD/C,0BAEF,CAEA,sBAIE,eAAgB,CAFhB,kBAAmB,CADnB,UAAY,CAEZ,cAEF,CAGA,YACE,kBACF,CAEA,YAIE,UAAW,CAHX,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,YAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAIlB,gCAAkC,CALlC,UAMF,CAEA,kBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,kBACE,oBACF,CAGA,OAEE,iBAAkB,CAElB,eAAgB,CADhB,kBAAmB,CAFnB,iBAIF,CAEA,aACE,qBAAsB,CAEtB,wBAAyB,CADzB,aAEF,CAEA,eACE,wBAAyB,CAEzB,wBAAyB,CADzB,aAEF,CAGA,SAOE,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAJ7B,oBAAqB,CAErB,WAAY,CAKZ,gBAAiB,CANjB,UAOF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,yBACE,WACE,YACF,CAEA,MACE,YACF,CAEA,KAEE,iBAAkB,CADlB,UAEF,CACF,CAEA,yBACE,WACE,YACF,CAEA,MACE,YACF,CAEA,YACE,cACF,CACF", "sources": ["index.css"], "sourcesContent": ["/* Global styles for Pakistan Income Tax Calculator */\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n  color: #333;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Container styles */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n/* Card styles */\n.card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  padding: 30px;\n  margin-bottom: 20px;\n}\n\n/* Button styles */\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-decoration: none;\n  display: inline-block;\n  text-align: center;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);\n}\n\n.btn-primary:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* Form styles */\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-input {\n  width: 100%;\n  padding: 12px 16px;\n  border: 2px solid #e1e5e9;\n  border-radius: 8px;\n  font-size: 16px;\n  transition: border-color 0.3s ease;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.form-input.error {\n  border-color: #e74c3c;\n}\n\n/* Alert styles */\n.alert {\n  padding: 12px 16px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  font-weight: 500;\n}\n\n.alert-error {\n  background-color: #fee;\n  color: #c53030;\n  border: 1px solid #fed7d7;\n}\n\n.alert-success {\n  background-color: #f0fff4;\n  color: #22543d;\n  border: 1px solid #c6f6d5;\n}\n\n/* Loading spinner */\n.loading {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-right: 8px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .container {\n    padding: 15px;\n  }\n  \n  .card {\n    padding: 20px;\n  }\n  \n  .btn {\n    width: 100%;\n    padding: 14px 24px;\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: 10px;\n  }\n  \n  .card {\n    padding: 15px;\n  }\n  \n  .form-input {\n    font-size: 16px; /* Prevent zoom on iOS */\n  }\n}\n"], "names": [], "sourceRoot": ""}